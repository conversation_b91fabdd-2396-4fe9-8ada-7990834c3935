"""
完整的Transformer实现 - 深度学习版本
包含编码器和解码器的完整实现，用于深入理解Transformer架构

学习目标：
1. 理解编码器-解码器架构的区别
2. 掌握不同类型的注意力机制（自注意力、交叉注意力）
3. 理解掩码机制的重要性
4. 学习序列到序列任务的实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import matplotlib.pyplot as plt
import numpy as np
from typing import Optional, Tuple

class PositionalEncoding(nn.Module):
    """
    位置编码：为序列中的每个位置添加位置信息
    使用sin和cos函数生成不同频率的位置编码
    """
    def __init__(self, d_model: int, max_len: int = 5000, dropout: float = 0.1):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        # 创建位置编码矩阵 [max_len, d_model]
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        # 计算不同频率的除数项
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                            (-math.log(10000.0) / d_model))
        
        # 偶数维度使用sin，奇数维度使用cos
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        # 调整维度为 [1, max_len, d_model] 以便广播
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: [batch_size, seq_len, d_model]
        Returns:
            x + positional_encoding: [batch_size, seq_len, d_model]
        """
        x = x + self.pe[:, :x.size(1), :]
        return self.dropout(x)

class MultiHeadAttention(nn.Module):
    """
    多头注意力机制
    将输入投影到多个子空间，并行计算注意力，最后合并结果
    """
    def __init__(self, d_model: int, n_heads: int, dropout: float = 0.1):
        super().__init__()
        assert d_model % n_heads == 0, "d_model必须能被n_heads整除"
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads  # 每个头的维度
        
        # 线性变换层
        self.W_q = nn.Linear(d_model, d_model, bias=False)
        self.W_k = nn.Linear(d_model, d_model, bias=False)
        self.W_v = nn.Linear(d_model, d_model, bias=False)
        self.W_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        
    def scaled_dot_product_attention(self, Q: torch.Tensor, K: torch.Tensor, 
                                   V: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        缩放点积注意力计算
        Attention(Q,K,V) = softmax(QK^T/√d_k)V
        """
        # 计算注意力分数 [batch_size, n_heads, seq_len, seq_len]
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        
        # 应用掩码（如果提供）
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # 计算注意力权重
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 计算加权输出
        output = torch.matmul(attention_weights, V)
        
        return output, attention_weights
    
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor, 
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            query, key, value: [batch_size, seq_len, d_model]
            mask: [batch_size, 1, seq_len, seq_len] 或 [batch_size, n_heads, seq_len, seq_len]
        Returns:
            output: [batch_size, seq_len, d_model]
            attention_weights: [batch_size, n_heads, seq_len, seq_len]
        """
        batch_size, seq_len = query.size(0), query.size(1)
        
        # 1. 线性变换并重塑为多头格式
        Q = self.W_q(query).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        K = self.W_k(key).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        V = self.W_v(value).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        
        # 2. 计算注意力
        attention_output, attention_weights = self.scaled_dot_product_attention(Q, K, V, mask)
        
        # 3. 拼接多头输出
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model)
        
        # 4. 最终线性变换
        output = self.W_o(attention_output)
        
        return output, attention_weights

class FeedForward(nn.Module):
    """
    位置前馈网络：FFN(x) = max(0, xW1 + b1)W2 + b2
    """
    def __init__(self, d_model: int, d_ff: int, dropout: float = 0.1):
        super().__init__()
        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.linear2(self.dropout(F.relu(self.linear1(x))))

class EncoderLayer(nn.Module):
    """
    Transformer编码器层
    包含：多头自注意力 + 前馈网络，每个子层都有残差连接和层归一化
    """
    def __init__(self, d_model: int, n_heads: int, d_ff: int, dropout: float = 0.1):
        super().__init__()
        self.self_attention = MultiHeadAttention(d_model, n_heads, dropout)
        self.feed_forward = FeedForward(d_model, d_ff, dropout)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            x: [batch_size, seq_len, d_model]
            mask: [batch_size, 1, seq_len, seq_len]
        """
        # 多头自注意力 + 残差连接 + 层归一化
        attn_output, attn_weights = self.self_attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # 前馈网络 + 残差连接 + 层归一化
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x, attn_weights

class TransformerEncoder(nn.Module):
    """
    完整的Transformer编码器
    """
    def __init__(self, vocab_size: int, d_model: int, n_heads: int, 
                 num_layers: int, d_ff: int, max_len: int = 5000, dropout: float = 0.1):
        super().__init__()
        self.d_model = d_model
        self.embedding = nn.Embedding(vocab_size, d_model)
        self.pos_encoding = PositionalEncoding(d_model, max_len, dropout)
        
        self.layers = nn.ModuleList([
            EncoderLayer(d_model, n_heads, d_ff, dropout) 
            for _ in range(num_layers)
        ])
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, list]:
        """
        Args:
            x: [batch_size, seq_len] - 输入token ids
            mask: [batch_size, 1, seq_len, seq_len] - 注意力掩码
        Returns:
            output: [batch_size, seq_len, d_model]
            attention_weights: list of attention weights from each layer
        """
        # 词嵌入 + 缩放
        x = self.embedding(x) * math.sqrt(self.d_model)
        
        # 位置编码
        x = self.pos_encoding(x)
        
        # 多层编码
        attention_weights = []
        for layer in self.layers:
            x, attn_weights = layer(x, mask)
            attention_weights.append(attn_weights)
        
        return x, attention_weights

def create_padding_mask(seq: torch.Tensor, pad_token: int = 0) -> torch.Tensor:
    """
    创建填充掩码，用于忽略填充位置的注意力
    Args:
        seq: [batch_size, seq_len]
        pad_token: 填充token的id
    Returns:
        mask: [batch_size, 1, 1, seq_len]
    """
    mask = (seq != pad_token).unsqueeze(1).unsqueeze(2)
    return mask

def create_look_ahead_mask(size: int) -> torch.Tensor:
    """
    创建前瞻掩码，防止解码器看到未来的信息
    Args:
        size: 序列长度
    Returns:
        mask: [size, size] 下三角矩阵
    """
    mask = torch.triu(torch.ones(size, size), diagonal=1)
    return mask == 0  # 转换为布尔掩码

# 测试函数
def test_encoder():
    """测试编码器实现"""
    print("=== 测试Transformer编码器 ===")
    
    # 超参数
    vocab_size = 1000
    d_model = 512
    n_heads = 8
    num_layers = 6
    d_ff = 2048
    seq_len = 20
    batch_size = 2
    
    # 创建模型
    encoder = TransformerEncoder(vocab_size, d_model, n_heads, num_layers, d_ff)
    
    # 创建测试数据（包含一些填充）
    test_input = torch.randint(1, vocab_size, (batch_size, seq_len))
    test_input[:, -5:] = 0  # 最后5个位置设为填充
    
    # 创建填充掩码
    padding_mask = create_padding_mask(test_input, pad_token=0)
    
    print(f"输入shape: {test_input.shape}")
    print(f"掩码shape: {padding_mask.shape}")
    
    # 前向传播
    with torch.no_grad():
        output, attention_weights = encoder(test_input, padding_mask)
    
    print(f"输出shape: {output.shape}")
    print(f"注意力权重层数: {len(attention_weights)}")
    print(f"每层注意力权重shape: {attention_weights[0].shape}")
    
    return encoder, test_input, output, attention_weights, padding_mask

class DecoderLayer(nn.Module):
    """
    Transformer解码器层
    包含：掩码多头自注意力 + 编码器-解码器注意力 + 前馈网络
    每个子层都有残差连接和层归一化
    """
    def __init__(self, d_model: int, n_heads: int, d_ff: int, dropout: float = 0.1):
        super().__init__()
        self.self_attention = MultiHeadAttention(d_model, n_heads, dropout)
        self.cross_attention = MultiHeadAttention(d_model, n_heads, dropout)
        self.feed_forward = FeedForward(d_model, d_ff, dropout)

        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor, encoder_output: torch.Tensor,
                look_ahead_mask: Optional[torch.Tensor] = None,
                padding_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Args:
            x: [batch_size, target_seq_len, d_model] - 解码器输入
            encoder_output: [batch_size, source_seq_len, d_model] - 编码器输出
            look_ahead_mask: [target_seq_len, target_seq_len] - 前瞻掩码
            padding_mask: [batch_size, 1, 1, source_seq_len] - 编码器填充掩码
        """
        # 1. 掩码多头自注意力
        attn1_output, attn1_weights = self.self_attention(x, x, x, look_ahead_mask)
        x = self.norm1(x + self.dropout(attn1_output))

        # 2. 编码器-解码器注意力（交叉注意力）
        attn2_output, attn2_weights = self.cross_attention(x, encoder_output, encoder_output, padding_mask)
        x = self.norm2(x + self.dropout(attn2_output))

        # 3. 前馈网络
        ff_output = self.feed_forward(x)
        x = self.norm3(x + self.dropout(ff_output))

        return x, attn1_weights, attn2_weights

class TransformerDecoder(nn.Module):
    """
    完整的Transformer解码器
    """
    def __init__(self, vocab_size: int, d_model: int, n_heads: int,
                 num_layers: int, d_ff: int, max_len: int = 5000, dropout: float = 0.1):
        super().__init__()
        self.d_model = d_model
        self.embedding = nn.Embedding(vocab_size, d_model)
        self.pos_encoding = PositionalEncoding(d_model, max_len, dropout)

        self.layers = nn.ModuleList([
            DecoderLayer(d_model, n_heads, d_ff, dropout)
            for _ in range(num_layers)
        ])

    def forward(self, x: torch.Tensor, encoder_output: torch.Tensor,
                look_ahead_mask: Optional[torch.Tensor] = None,
                padding_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, list, list]:
        """
        Args:
            x: [batch_size, target_seq_len] - 目标序列token ids
            encoder_output: [batch_size, source_seq_len, d_model] - 编码器输出
            look_ahead_mask: [target_seq_len, target_seq_len] - 前瞻掩码
            padding_mask: [batch_size, 1, 1, source_seq_len] - 编码器填充掩码
        """
        # 词嵌入 + 缩放
        x = self.embedding(x) * math.sqrt(self.d_model)

        # 位置编码
        x = self.pos_encoding(x)

        # 多层解码
        self_attention_weights = []
        cross_attention_weights = []

        for layer in self.layers:
            x, self_attn_weights, cross_attn_weights = layer(
                x, encoder_output, look_ahead_mask, padding_mask)
            self_attention_weights.append(self_attn_weights)
            cross_attention_weights.append(cross_attn_weights)

        return x, self_attention_weights, cross_attention_weights

class Transformer(nn.Module):
    """
    完整的Transformer模型（编码器-解码器架构）
    """
    def __init__(self, src_vocab_size: int, tgt_vocab_size: int, d_model: int = 512,
                 n_heads: int = 8, num_encoder_layers: int = 6, num_decoder_layers: int = 6,
                 d_ff: int = 2048, max_len: int = 5000, dropout: float = 0.1):
        super().__init__()

        self.encoder = TransformerEncoder(
            src_vocab_size, d_model, n_heads, num_encoder_layers, d_ff, max_len, dropout)

        self.decoder = TransformerDecoder(
            tgt_vocab_size, d_model, n_heads, num_decoder_layers, d_ff, max_len, dropout)

        # 输出投影层
        self.output_projection = nn.Linear(d_model, tgt_vocab_size)

    def forward(self, src: torch.Tensor, tgt: torch.Tensor,
                src_mask: Optional[torch.Tensor] = None,
                tgt_mask: Optional[torch.Tensor] = None,
                memory_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            src: [batch_size, src_seq_len] - 源序列
            tgt: [batch_size, tgt_seq_len] - 目标序列
            src_mask: 源序列填充掩码
            tgt_mask: 目标序列前瞻掩码
            memory_mask: 编码器输出的填充掩码
        """
        # 编码
        encoder_output, _ = self.encoder(src, src_mask)

        # 解码
        decoder_output, _, _ = self.decoder(tgt, encoder_output, tgt_mask, memory_mask)

        # 输出投影
        output = self.output_projection(decoder_output)

        return output

def test_complete_transformer():
    """测试完整的Transformer模型"""
    print("=== 测试完整Transformer模型 ===")

    # 超参数
    src_vocab_size = 1000
    tgt_vocab_size = 800
    d_model = 512
    n_heads = 8
    num_layers = 6
    d_ff = 2048
    src_seq_len = 15
    tgt_seq_len = 12
    batch_size = 2

    # 创建完整模型
    model = Transformer(src_vocab_size, tgt_vocab_size, d_model, n_heads, num_layers, num_layers, d_ff)

    # 创建测试数据
    src_input = torch.randint(1, src_vocab_size, (batch_size, src_seq_len))
    tgt_input = torch.randint(1, tgt_vocab_size, (batch_size, tgt_seq_len))

    # 添加一些填充
    src_input[:, -3:] = 0
    tgt_input[:, -2:] = 0

    # 创建掩码
    src_mask = create_padding_mask(src_input, pad_token=0)
    tgt_look_ahead_mask = create_look_ahead_mask(tgt_seq_len)
    tgt_padding_mask = create_padding_mask(tgt_input, pad_token=0)

    # 组合目标掩码（前瞻掩码 + 填充掩码）
    # 扩展前瞻掩码的维度以匹配填充掩码
    tgt_look_ahead_expanded = tgt_look_ahead_mask.unsqueeze(0).unsqueeze(0).expand(batch_size, 1, tgt_seq_len, tgt_seq_len)
    tgt_mask = torch.minimum(tgt_look_ahead_expanded, tgt_padding_mask)

    print(f"源序列shape: {src_input.shape}")
    print(f"目标序列shape: {tgt_input.shape}")
    print(f"前瞻掩码shape: {tgt_look_ahead_mask.shape}")

    # 前向传播
    with torch.no_grad():
        output = model(src_input, tgt_input, src_mask, tgt_mask, src_mask)

    print(f"输出shape: {output.shape}")
    print(f"输出词汇表大小: {output.size(-1)}")

    return model, src_input, tgt_input, output

if __name__ == "__main__":
    # 测试编码器
    print("1. 测试编码器...")
    encoder, test_input, output, attention_weights, mask = test_encoder()

    print("\n" + "="*50)

    # 测试完整模型
    print("2. 测试完整Transformer...")
    model, src_input, tgt_input, output = test_complete_transformer()

    print("\n" + "="*50)
    print("🎉 完整的Transformer实现测试成功！")
    print("\n接下来的学习建议：")
    print("1. 理解每种注意力机制的区别")
    print("2. 分析掩码机制的作用")
    print("3. 可视化注意力权重")
    print("4. 在具体任务上训练模型")
