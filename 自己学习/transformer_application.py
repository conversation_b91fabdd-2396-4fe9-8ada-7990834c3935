"""
Transformer实际应用示例
演示如何在具体任务上训练和使用Transformer模型

任务：简单的序列到序列翻译（数字序列转换）
目标：将输入序列的每个数字加1，例如 [1,2,3] -> [2,3,4]
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import matplotlib.pyplot as plt
from complete_transformer import Transformer, create_padding_mask, create_look_ahead_mask
import random

class NumberSequenceDataset(Dataset):
    """数字序列数据集"""
    
    def __init__(self, num_samples=10000, min_len=3, max_len=10, vocab_size=50):
        self.num_samples = num_samples
        self.min_len = min_len
        self.max_len = max_len
        self.vocab_size = vocab_size
        self.pad_token = 0
        self.start_token = vocab_size + 1
        self.end_token = vocab_size + 2
        
        # 生成数据
        self.data = self._generate_data()
    
    def _generate_data(self):
        """生成训练数据"""
        data = []
        for _ in range(self.num_samples):
            # 随机生成序列长度
            seq_len = random.randint(self.min_len, self.max_len)
            
            # 生成输入序列（避免使用特殊token）
            src_seq = [random.randint(1, self.vocab_size) for _ in range(seq_len)]
            
            # 生成目标序列（每个数字加1，但不超过vocab_size）
            tgt_seq = [min(x + 1, self.vocab_size) for x in src_seq]
            
            # 为目标序列添加开始和结束token
            tgt_input = [self.start_token] + tgt_seq
            tgt_output = tgt_seq + [self.end_token]
            
            data.append({
                'src': src_seq,
                'tgt_input': tgt_input,
                'tgt_output': tgt_output
            })
        
        return data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return self.data[idx]

def collate_fn(batch):
    """批处理函数"""
    # 找到最大长度
    max_src_len = max(len(item['src']) for item in batch)
    max_tgt_len = max(len(item['tgt_input']) for item in batch)
    
    # 填充序列
    src_batch = []
    tgt_input_batch = []
    tgt_output_batch = []
    
    for item in batch:
        # 源序列填充
        src_padded = item['src'] + [0] * (max_src_len - len(item['src']))
        src_batch.append(src_padded)
        
        # 目标输入序列填充
        tgt_input_padded = item['tgt_input'] + [0] * (max_tgt_len - len(item['tgt_input']))
        tgt_input_batch.append(tgt_input_padded)
        
        # 目标输出序列填充
        tgt_output_padded = item['tgt_output'] + [0] * (max_tgt_len - len(item['tgt_output']))
        tgt_output_batch.append(tgt_output_padded)
    
    return {
        'src': torch.tensor(src_batch, dtype=torch.long),
        'tgt_input': torch.tensor(tgt_input_batch, dtype=torch.long),
        'tgt_output': torch.tensor(tgt_output_batch, dtype=torch.long)
    }

class TransformerTrainer:
    """Transformer训练器"""
    
    def __init__(self, model, train_loader, val_loader, device='cpu'):
        self.model = model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.device = device
        
        # 优化器和损失函数
        self.optimizer = optim.Adam(model.parameters(), lr=0.0001, betas=(0.9, 0.98), eps=1e-9)
        self.criterion = nn.CrossEntropyLoss(ignore_index=0)  # 忽略填充token
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.val_accuracies = []
    
    def create_masks(self, src, tgt_input):
        """创建掩码"""
        # 源序列填充掩码
        src_mask = create_padding_mask(src, pad_token=0)
        
        # 目标序列掩码（前瞻掩码 + 填充掩码）
        tgt_len = tgt_input.size(1)
        look_ahead_mask = create_look_ahead_mask(tgt_len).to(self.device)
        tgt_padding_mask = create_padding_mask(tgt_input, pad_token=0)
        
        # 组合掩码
        tgt_mask = torch.minimum(
            look_ahead_mask.unsqueeze(0).unsqueeze(0),
            tgt_padding_mask
        )
        
        return src_mask.to(self.device), tgt_mask.to(self.device)
    
    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        for batch in self.train_loader:
            src = batch['src'].to(self.device)
            tgt_input = batch['tgt_input'].to(self.device)
            tgt_output = batch['tgt_output'].to(self.device)
            
            # 创建掩码
            src_mask, tgt_mask = self.create_masks(src, tgt_input)
            
            # 前向传播
            self.optimizer.zero_grad()
            output = self.model(src, tgt_input, src_mask, tgt_mask, src_mask)
            
            # 计算损失
            loss = self.criterion(output.reshape(-1, output.size(-1)), tgt_output.reshape(-1))
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        return total_loss / num_batches
    
    def validate(self):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        total_correct = 0
        total_tokens = 0
        num_batches = 0
        
        with torch.no_grad():
            for batch in self.val_loader:
                src = batch['src'].to(self.device)
                tgt_input = batch['tgt_input'].to(self.device)
                tgt_output = batch['tgt_output'].to(self.device)
                
                # 创建掩码
                src_mask, tgt_mask = self.create_masks(src, tgt_input)
                
                # 前向传播
                output = self.model(src, tgt_input, src_mask, tgt_mask, src_mask)
                
                # 计算损失
                loss = self.criterion(output.reshape(-1, output.size(-1)), tgt_output.reshape(-1))
                total_loss += loss.item()
                
                # 计算准确率（忽略填充token）
                predictions = torch.argmax(output, dim=-1)
                mask = (tgt_output != 0)
                correct = (predictions == tgt_output) & mask
                total_correct += correct.sum().item()
                total_tokens += mask.sum().item()
                
                num_batches += 1
        
        avg_loss = total_loss / num_batches
        accuracy = total_correct / total_tokens if total_tokens > 0 else 0
        
        return avg_loss, accuracy
    
    def train(self, num_epochs):
        """训练模型"""
        print(f"开始训练，共{num_epochs}个epoch...")
        
        for epoch in range(num_epochs):
            # 训练
            train_loss = self.train_epoch()
            
            # 验证
            val_loss, val_accuracy = self.validate()
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.val_accuracies.append(val_accuracy)
            
            print(f"Epoch {epoch+1}/{num_epochs}:")
            print(f"  训练损失: {train_loss:.4f}")
            print(f"  验证损失: {val_loss:.4f}")
            print(f"  验证准确率: {val_accuracy:.4f}")
            print("-" * 40)
    
    def plot_training_history(self):
        """绘制训练历史"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # 损失曲线
        ax1.plot(self.train_losses, label='训练损失', color='blue')
        ax1.plot(self.val_losses, label='验证损失', color='red')
        ax1.set_title('训练和验证损失')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # 准确率曲线
        ax2.plot(self.val_accuracies, label='验证准确率', color='green')
        ax2.set_title('验证准确率')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.show()

def test_model_inference(model, dataset, device='cpu'):
    """测试模型推理"""
    model.eval()
    
    # 随机选择几个测试样本
    test_samples = random.sample(dataset.data, 5)
    
    print("=== 模型推理测试 ===")
    
    with torch.no_grad():
        for i, sample in enumerate(test_samples):
            src = torch.tensor([sample['src']], dtype=torch.long).to(device)
            
            print(f"\n测试样本 {i+1}:")
            print(f"输入序列: {sample['src']}")
            print(f"期望输出: {sample['tgt_output'][:-1]}")  # 去掉结束token
            
            # 简单的贪心解码
            max_len = len(sample['src']) + 2
            tgt_input = torch.tensor([[dataset.start_token]], dtype=torch.long).to(device)
            
            for _ in range(max_len):
                src_mask = create_padding_mask(src, pad_token=0).to(device)
                tgt_mask = create_look_ahead_mask(tgt_input.size(1)).unsqueeze(0).unsqueeze(0).to(device)
                
                output = model(src, tgt_input, src_mask, tgt_mask, src_mask)
                next_token = torch.argmax(output[0, -1, :]).item()
                
                if next_token == dataset.end_token:
                    break
                
                tgt_input = torch.cat([tgt_input, torch.tensor([[next_token]], dtype=torch.long).to(device)], dim=1)
            
            predicted = tgt_input[0, 1:].cpu().tolist()  # 去掉开始token
            if dataset.end_token in predicted:
                predicted = predicted[:predicted.index(dataset.end_token)]
            
            print(f"模型输出: {predicted}")
            
            # 检查是否正确
            expected = sample['tgt_output'][:-1]
            is_correct = predicted == expected
            print(f"预测正确: {'✅' if is_correct else '❌'}")

def main():
    """主函数"""
    print("🚀 开始Transformer应用示例...")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建数据集
    print("\n📊 创建数据集...")
    vocab_size = 20
    train_dataset = NumberSequenceDataset(num_samples=5000, vocab_size=vocab_size)
    val_dataset = NumberSequenceDataset(num_samples=1000, vocab_size=vocab_size)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False, collate_fn=collate_fn)
    
    # 创建模型
    print("\n🏗️ 创建模型...")
    model = Transformer(
        src_vocab_size=vocab_size + 3,  # +3 for pad, start, end tokens
        tgt_vocab_size=vocab_size + 3,
        d_model=128,
        n_heads=8,
        num_encoder_layers=3,
        num_decoder_layers=3,
        d_ff=512,
        dropout=0.1
    )
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练器
    trainer = TransformerTrainer(model, train_loader, val_loader, device)
    
    # 训练模型
    print("\n🎯 开始训练...")
    trainer.train(num_epochs=20)
    
    # 绘制训练历史
    print("\n📈 绘制训练历史...")
    trainer.plot_training_history()
    
    # 测试模型
    print("\n🧪 测试模型推理...")
    test_model_inference(model, train_dataset, device)
    
    print("\n✅ 应用示例完成！")
    print("\n💡 学习要点总结：")
    print("1. 数据预处理和批处理的重要性")
    print("2. 掩码机制在训练中的应用")
    print("3. 序列到序列任务的训练流程")
    print("4. 模型推理和解码策略")

if __name__ == "__main__":
    main()
