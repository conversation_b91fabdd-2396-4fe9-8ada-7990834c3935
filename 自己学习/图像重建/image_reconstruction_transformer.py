"""
基于Transformer的图像重建实现
支持多种图像重建任务：图像修复、超分辨率、去噪等

核心思想：
1. 将图像分割成patches，类似ViT
2. 使用Transformer编码器提取特征
3. 使用专门的解码器重建图像
4. 支持掩码机制进行图像修复
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
from complete_transformer import MultiHeadAttention, FeedForward, PositionalEncoding
from typing import Optional, Tuple
import matplotlib.pyplot as plt

class PatchEmbedding(nn.Module):
    """图像分块嵌入层"""
    
    def __init__(self, img_size: int = 224, patch_size: int = 16, in_channels: int = 3, embed_dim: int = 768):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2
        self.patch_dim = in_channels * patch_size * patch_size
        
        # 线性投影层
        self.projection = nn.Linear(self.patch_dim, embed_dim)
        
        # 可学习的位置编码
        self.pos_embedding = nn.Parameter(torch.randn(1, self.num_patches, embed_dim))
        
        # CLS token（可选，用于全局特征）
        self.cls_token = nn.Parameter(torch.randn(1, 1, embed_dim))
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: [batch_size, channels, height, width]
        Returns:
            patches: [batch_size, num_patches + 1, embed_dim]
        """
        batch_size = x.shape[0]
        
        # 分割成patches [batch_size, num_patches, patch_dim]
        patches = x.unfold(2, self.patch_size, self.patch_size).unfold(3, self.patch_size, self.patch_size)
        patches = patches.contiguous().view(batch_size, self.num_patches, -1)
        
        # 线性投影
        patches = self.projection(patches)
        
        # 添加位置编码
        patches = patches + self.pos_embedding
        
        # 添加CLS token
        cls_tokens = self.cls_token.expand(batch_size, -1, -1)
        patches = torch.cat([cls_tokens, patches], dim=1)
        
        return patches

class ImageReconstructionDecoder(nn.Module):
    """图像重建解码器"""
    
    def __init__(self, embed_dim: int = 768, patch_size: int = 16, out_channels: int = 3, 
                 num_layers: int = 4, n_heads: int = 8, d_ff: int = 2048):
        super().__init__()
        self.embed_dim = embed_dim
        self.patch_size = patch_size
        self.out_channels = out_channels
        self.patch_dim = out_channels * patch_size * patch_size
        
        # Transformer解码器层
        self.decoder_layers = nn.ModuleList([
            ImageDecoderLayer(embed_dim, n_heads, d_ff)
            for _ in range(num_layers)
        ])
        
        # 输出投影层
        self.output_projection = nn.Linear(embed_dim, self.patch_dim)
        
        # 层归一化
        self.norm = nn.LayerNorm(embed_dim)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            x: [batch_size, num_patches + 1, embed_dim]
            mask: [batch_size, num_patches + 1] - 掩码，1表示保留，0表示掩盖
        Returns:
            reconstructed_patches: [batch_size, num_patches, patch_dim]
        """
        # 移除CLS token
        x = x[:, 1:, :]  # [batch_size, num_patches, embed_dim]
        
        # 通过解码器层
        for layer in self.decoder_layers:
            x = layer(x, mask)
        
        # 层归一化
        x = self.norm(x)
        
        # 输出投影
        reconstructed_patches = self.output_projection(x)
        
        return reconstructed_patches

class ImageDecoderLayer(nn.Module):
    """图像解码器层"""
    
    def __init__(self, embed_dim: int, n_heads: int, d_ff: int, dropout: float = 0.1):
        super().__init__()
        self.self_attention = MultiHeadAttention(embed_dim, n_heads, dropout)
        self.feed_forward = FeedForward(embed_dim, d_ff, dropout)
        self.norm1 = nn.LayerNorm(embed_dim)
        self.norm2 = nn.LayerNorm(embed_dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        # 自注意力（暂时不使用掩码，因为维度处理复杂）
        attn_output, _ = self.self_attention(x, x, x, None)
        x = self.norm1(x + self.dropout(attn_output))

        # 前馈网络
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))

        return x

class ImageReconstructionTransformer(nn.Module):
    """完整的图像重建Transformer"""
    
    def __init__(self, img_size: int = 224, patch_size: int = 16, in_channels: int = 3,
                 embed_dim: int = 768, encoder_layers: int = 12, decoder_layers: int = 4,
                 n_heads: int = 12, d_ff: int = 3072, dropout: float = 0.1):
        super().__init__()
        
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2
        
        # 图像分块嵌入
        self.patch_embedding = PatchEmbedding(img_size, patch_size, in_channels, embed_dim)
        
        # 编码器（使用现有的Transformer编码器层）
        from complete_transformer import EncoderLayer
        self.encoder_layers = nn.ModuleList([
            EncoderLayer(embed_dim, n_heads, d_ff, dropout)
            for _ in range(encoder_layers)
        ])
        
        # 解码器
        self.decoder = ImageReconstructionDecoder(embed_dim, patch_size, in_channels, 
                                                 decoder_layers, n_heads, d_ff)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask_ratio: float = 0.0) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            x: [batch_size, channels, height, width]
            mask_ratio: 掩码比例，用于图像修复任务
        Returns:
            reconstructed: [batch_size, channels, height, width]
            mask: [batch_size, num_patches] - 实际使用的掩码
        """
        batch_size = x.shape[0]
        
        # 图像分块嵌入
        patches = self.patch_embedding(x)  # [batch_size, num_patches + 1, embed_dim]
        patches = self.dropout(patches)
        
        # 创建掩码（如果需要）
        mask = None
        if mask_ratio > 0:
            mask = self.create_random_mask(batch_size, mask_ratio)
            # 应用掩码到patches（除了CLS token）
            # 将被掩盖的patches设为0
            mask_expanded = mask.unsqueeze(-1).expand(-1, -1, patches.size(-1))
            patches[:, 1:, :] = patches[:, 1:, :] * mask_expanded
        
        # 编码器
        for layer in self.encoder_layers:
            patches, _ = layer(patches)
        
        # 解码器
        reconstructed_patches = self.decoder(patches, mask)
        
        # 重建图像
        reconstructed = self.patches_to_image(reconstructed_patches)
        
        return reconstructed, mask
    
    def create_random_mask(self, batch_size: int, mask_ratio: float) -> torch.Tensor:
        """创建随机掩码"""
        num_masked = int(self.num_patches * mask_ratio)
        mask = torch.ones(batch_size, self.num_patches)
        
        for i in range(batch_size):
            masked_indices = torch.randperm(self.num_patches)[:num_masked]
            mask[i, masked_indices] = 0
        
        return mask.to(next(self.parameters()).device)
    
    def patches_to_image(self, patches: torch.Tensor) -> torch.Tensor:
        """将patches重建为图像"""
        batch_size = patches.shape[0]
        patches_per_side = self.img_size // self.patch_size
        
        # 重塑patches
        patches = patches.view(batch_size, patches_per_side, patches_per_side, 
                              3, self.patch_size, self.patch_size)
        
        # 重建图像
        image = patches.permute(0, 3, 1, 4, 2, 5).contiguous()
        image = image.view(batch_size, 3, self.img_size, self.img_size)
        
        return image

class ImageInpaintingTransformer(ImageReconstructionTransformer):
    """专门用于图像修复的Transformer"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
    def forward_inpainting(self, x: torch.Tensor, mask: torch.Tensor) -> torch.Tensor:
        """
        图像修复前向传播
        Args:
            x: [batch_size, channels, height, width] - 输入图像
            mask: [batch_size, 1, height, width] - 掩码，0表示需要修复的区域
        """
        # 应用掩码到输入图像
        masked_image = x * mask
        
        # 使用Transformer重建
        reconstructed, _ = self.forward(masked_image, mask_ratio=0.0)
        
        # 只在掩码区域使用重建结果
        result = x * mask + reconstructed * (1 - mask)
        
        return result

def create_test_image():
    """创建一个简单的测试图像"""
    # 创建一个有明显特征的图像
    img = torch.zeros(1, 3, 224, 224)

    # 红色方块
    img[0, 0, 50:100, 50:100] = 1.0

    # 绿色圆形
    center_y, center_x = 112, 150
    y, x = torch.meshgrid(torch.arange(224), torch.arange(224), indexing='ij')
    circle_mask = ((y - center_y)**2 + (x - center_x)**2) < 30**2
    img[0, 1, circle_mask] = 1.0

    # 蓝色条纹
    img[0, 2, 150:200, :] = 1.0

    return img

def visualize_reconstruction(original, reconstructed, masked_input=None, save_path="reconstruction_result.png"):
    """可视化重建结果"""
    import matplotlib.pyplot as plt

    # 确保数据在正确范围内
    original = torch.clamp(original, 0, 1)
    reconstructed = torch.clamp(reconstructed, 0, 1)

    if masked_input is not None:
        _, axes = plt.subplots(1, 3, figsize=(15, 5))

        # 原始图像
        axes[0].imshow(original[0].permute(1, 2, 0).cpu().numpy())
        axes[0].set_title('Original Image')
        axes[0].axis('off')

        # 掩码输入
        axes[1].imshow(masked_input[0].permute(1, 2, 0).cpu().numpy())
        axes[1].set_title('Masked Input')
        axes[1].axis('off')

        # 重建图像
        axes[2].imshow(reconstructed[0].permute(1, 2, 0).cpu().numpy())
        axes[2].set_title('Reconstructed Image')
        axes[2].axis('off')
    else:
        _, axes = plt.subplots(1, 2, figsize=(10, 5))

        # 原始图像
        axes[0].imshow(original[0].permute(1, 2, 0).cpu().numpy())
        axes[0].set_title('Original Image')
        axes[0].axis('off')

        # 重建图像
        axes[1].imshow(reconstructed[0].permute(1, 2, 0).cpu().numpy())
        axes[1].set_title('Reconstructed Image')
        axes[1].axis('off')

    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"可视化结果已保存到: {save_path}")

def test_image_reconstruction():
    """测试图像重建Transformer"""
    print("=== 测试图像重建Transformer ===")

    # 创建一个较小的模型用于测试
    model = ImageReconstructionTransformer(
        img_size=224,
        patch_size=16,
        in_channels=3,
        embed_dim=256,  # 减小模型大小
        encoder_layers=4,
        decoder_layers=2,
        n_heads=8,
        d_ff=1024
    )

    # 创建测试图像
    test_image = create_test_image()

    print(f"输入图像shape: {test_image.shape}")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

    # 测试完整重建（无掩码）
    print("\n1. 测试完整图像重建...")
    with torch.no_grad():
        reconstructed, _ = model(test_image, mask_ratio=0.0)

    print(f"重建图像shape: {reconstructed.shape}")
    mse_loss = F.mse_loss(reconstructed, test_image).item()
    print(f"重建误差 (MSE): {mse_loss:.6f}")

    # 可视化完整重建
    visualize_reconstruction(test_image, reconstructed, save_path="complete_reconstruction.png")

    # 测试图像修复（有掩码）
    print("\n2. 测试图像修复...")
    with torch.no_grad():
        reconstructed_masked, mask = model(test_image, mask_ratio=0.5)

    # 创建掩码输入用于可视化
    mask_expanded = mask.unsqueeze(-1).expand(-1, -1, test_image.size(-1))
    masked_input = test_image.clone()
    masked_input[:, :, 1:] = masked_input[:, :, 1:] * mask_expanded.permute(0, 2, 1).unsqueeze(1)

    print(f"掩码比例: 50%")
    print(f"被掩盖的patches数量: {(mask == 0).sum().item()}/{mask.numel()}")

    # 可视化修复结果
    visualize_reconstruction(test_image, reconstructed_masked, masked_input, "inpainting_reconstruction.png")

    return model, test_image, reconstructed, reconstructed_masked, mask

def test_simple_inpainting():
    """测试简单的图像修复"""
    print("\n=== 测试简单图像修复 ===")

    # 创建测试图像
    test_image = create_test_image()

    # 创建修复掩码（移除红色方块）
    mask = torch.ones_like(test_image)
    mask[:, :, 50:100, 50:100] = 0  # 移除红色方块区域

    # 应用掩码
    masked_image = test_image * mask

    print(f"原始图像shape: {test_image.shape}")
    print(f"掩码图像shape: {masked_image.shape}")
    print(f"需要修复的像素数: {(mask == 0).sum().item()}")

    # 可视化修复过程
    visualize_reconstruction(test_image, masked_image, masked_image, "simple_inpainting_demo.png")

    return test_image, masked_image, mask

if __name__ == "__main__":
    print("🖼️ 开始测试图像重建Transformer...")
    
    # 测试基础重建
    model, test_image, reconstructed, reconstructed_masked, mask = test_image_reconstruction()
    
    # 测试简单图像修复演示
    test_img, masked_img, repair_mask = test_simple_inpainting()
    
    print("\n✅ 图像重建Transformer测试完成！")
    print("\n💡 应用场景：")
    print("1. 图像修复：去除图像中的不需要对象")
    print("2. 图像去噪：去除图像中的噪声")
    print("3. 超分辨率：提高图像分辨率")
    print("4. 图像补全：填补图像中的缺失区域")
    print("5. 风格迁移：改变图像的艺术风格")
    
    print("\n🔗 与您的项目结合：")
    print("1. 可以结合T-UDeepSC进行语义通信中的图像重建")
    print("2. 用于3D重建中的多视图图像修复和增强")
    print("3. 在语义压缩后进行图像质量恢复")
