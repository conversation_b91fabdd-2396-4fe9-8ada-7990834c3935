"""
基于交通场景数据集的图像重建Transformer
使用真实的交通场景数据进行训练和测试

支持的数据集:
1. CIFAR-10 (包含汽车、卡车类别)
2. 自定义交通场景数据集
3. 可扩展到KITTI、Cityscapes等大型数据集
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, Subset
import torchvision
import torchvision.transforms as transforms
import matplotlib.pyplot as plt
import numpy as np
import os
from PIL import Image

# 导入我们之前的Transformer模型
import sys
sys.path.append('.')
from simple_image_transformer import SimpleImageTransformer

class TrafficSceneDataset(Dataset):
    """交通场景数据集"""
    
    def __init__(self, root_dir, transform=None, subset_classes=None):
        """
        Args:
            root_dir: 数据集根目录
            transform: 数据变换
            subset_classes: 要使用的类别子集
        """
        self.root_dir = root_dir
        self.transform = transform
        self.subset_classes = subset_classes
        
        # 如果是CIFAR-10，使用torchvision加载
        if 'cifar' in root_dir.lower():
            self.use_cifar = True
            self.dataset = torchvision.datasets.CIFAR10(
                root=root_dir, train=True, download=True, transform=None)
            
            # CIFAR-10类别: 0-飞机, 1-汽车, 2-鸟, 3-猫, 4-鹿, 5-狗, 6-青蛙, 7-马, 8-船, 9-卡车
            if subset_classes is None:
                # 默认使用交通相关类别: 汽车(1), 卡车(9), 船(8)
                self.subset_classes = [1, 8, 9]
            
            # 筛选交通相关的样本
            self.indices = [i for i, (_, label) in enumerate(self.dataset) 
                           if label in self.subset_classes]
        else:
            self.use_cifar = False
            # 自定义数据集加载逻辑
            self.image_paths = self._load_custom_dataset()
    
    def _load_custom_dataset(self):
        """加载自定义数据集"""
        image_paths = []
        for root, dirs, files in os.walk(self.root_dir):
            for file in files:
                if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                    image_paths.append(os.path.join(root, file))
        return image_paths
    
    def __len__(self):
        if self.use_cifar:
            return len(self.indices)
        else:
            return len(self.image_paths)
    
    def __getitem__(self, idx):
        if self.use_cifar:
            real_idx = self.indices[idx]
            image, label = self.dataset[real_idx]
            
            # 转换为tensor
            if self.transform:
                image = self.transform(image)
            else:
                image = transforms.ToTensor()(image)
                
            return image, image  # 输入和目标相同（自监督）
        else:
            image_path = self.image_paths[idx]
            image = Image.open(image_path).convert('RGB')
            
            if self.transform:
                image = self.transform(image)
            
            return image, image

def create_traffic_data_loaders(data_dir='./data', batch_size=32, img_size=64):
    """创建交通场景数据加载器"""
    
    # 数据变换
    transform = transforms.Compose([
        transforms.Resize((img_size, img_size)),
        transforms.ToTensor(),
        transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))  # 归一化到[-1,1]
    ])
    
    # 创建数据集
    print("正在加载交通场景数据集...")
    dataset = TrafficSceneDataset(data_dir, transform=transform)
    
    print(f"数据集大小: {len(dataset)}")
    
    # 划分训练集和验证集
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    
    train_indices = list(range(train_size))
    val_indices = list(range(train_size, len(dataset)))
    
    train_dataset = Subset(dataset, train_indices)
    val_dataset = Subset(dataset, val_indices)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    print(f"训练集大小: {len(train_dataset)}")
    print(f"验证集大小: {len(val_dataset)}")
    
    return train_loader, val_loader

def visualize_traffic_samples(data_loader, num_samples=8, save_path="traffic_samples.png"):
    """可视化交通场景样本"""
    
    # 获取一批数据
    data_iter = iter(data_loader)
    images, _ = next(data_iter)
    
    # 反归一化
    images = (images + 1) / 2  # 从[-1,1]转换到[0,1]
    
    fig, axes = plt.subplots(2, 4, figsize=(12, 6))
    axes = axes.flatten()
    
    for i in range(min(num_samples, len(images))):
        img = images[i].permute(1, 2, 0).cpu().numpy()
        img = np.clip(img, 0, 1)
        
        axes[i].imshow(img)
        axes[i].set_title(f'Traffic Scene {i+1}')
        axes[i].axis('off')
    
    # 隐藏多余的子图
    for i in range(num_samples, len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"交通场景样本已保存到: {save_path}")

class TrafficSceneTrainer:
    """交通场景重建训练器"""
    
    def __init__(self, model, train_loader, val_loader, device='cpu'):
        self.model = model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.device = device
        
        # 优化器和损失函数
        self.optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
        self.scheduler = optim.lr_scheduler.StepLR(self.optimizer, step_size=20, gamma=0.5)
        self.criterion = nn.MSELoss()
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.val_psnr = []
    
    def calculate_psnr(self, img1, img2):
        """计算PSNR"""
        mse = torch.mean((img1 - img2) ** 2)
        if mse == 0:
            return float('inf')
        return 20 * torch.log10(1.0 / torch.sqrt(mse))
    
    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        for batch_idx, (data, target) in enumerate(self.train_loader):
            data, target = data.to(self.device), target.to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            output = self.model(data)
            
            # 计算损失
            loss = self.criterion(output, target)
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx % 50 == 0:
                print(f'  Batch {batch_idx}/{len(self.train_loader)}, Loss: {loss.item():.6f}')
        
        return total_loss / num_batches
    
    def validate(self):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        total_psnr = 0
        num_batches = 0
        
        with torch.no_grad():
            for data, target in self.val_loader:
                data, target = data.to(self.device), target.to(self.device)
                
                # 前向传播
                output = self.model(data)
                
                # 计算损失
                loss = self.criterion(output, target)
                total_loss += loss.item()
                
                # 计算PSNR
                psnr = self.calculate_psnr(output, target)
                total_psnr += psnr.item()
                
                num_batches += 1
        
        avg_loss = total_loss / num_batches
        avg_psnr = total_psnr / num_batches
        
        return avg_loss, avg_psnr
    
    def train(self, num_epochs):
        """训练模型"""
        print(f"开始训练交通场景重建模型，共{num_epochs}个epoch...")
        
        best_psnr = 0
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            
            # 训练
            train_loss = self.train_epoch()
            
            # 验证
            val_loss, val_psnr = self.validate()
            
            # 更新学习率
            self.scheduler.step()
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.val_psnr.append(val_psnr)
            
            print(f"训练损失: {train_loss:.6f}")
            print(f"验证损失: {val_loss:.6f}")
            print(f"验证PSNR: {val_psnr:.2f} dB")
            
            # 保存最佳模型
            if val_psnr > best_psnr:
                best_psnr = val_psnr
                torch.save(self.model.state_dict(), 'best_traffic_model.pth')
                print(f"保存最佳模型 (PSNR: {best_psnr:.2f} dB)")
    
    def plot_training_history(self, save_path="traffic_training_history.png"):
        """绘制训练历史"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # 损失曲线
        ax1.plot(self.train_losses, label='训练损失', color='blue')
        ax1.plot(self.val_losses, label='验证损失', color='red')
        ax1.set_title('训练和验证损失')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # PSNR曲线
        ax2.plot(self.val_psnr, label='验证PSNR', color='green')
        ax2.set_title('验证PSNR')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('PSNR (dB)')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        print(f"训练历史已保存到: {save_path}")

def test_traffic_reconstruction(model, test_loader, device='cpu', save_path="traffic_reconstruction_results.png"):
    """测试交通场景重建"""
    model.eval()
    
    # 获取测试样本
    data_iter = iter(test_loader)
    images, _ = next(data_iter)
    images = images[:4].to(device)  # 取前4个样本
    
    with torch.no_grad():
        reconstructed = model(images)
    
    # 反归一化
    images = (images + 1) / 2
    reconstructed = (reconstructed + 1) / 2
    
    # 可视化
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    
    for i in range(4):
        # 原始图像
        img = images[i].permute(1, 2, 0).cpu().numpy()
        img = np.clip(img, 0, 1)
        axes[0, i].imshow(img)
        axes[0, i].set_title(f'Original {i+1}')
        axes[0, i].axis('off')
        
        # 重建图像
        recon_img = reconstructed[i].permute(1, 2, 0).cpu().numpy()
        recon_img = np.clip(recon_img, 0, 1)
        axes[1, i].imshow(recon_img)
        axes[1, i].set_title(f'Reconstructed {i+1}')
        axes[1, i].axis('off')
    
    plt.suptitle('Traffic Scene Reconstruction Results')
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"重建结果已保存到: {save_path}")

def main():
    """主函数"""
    print("🚗 交通场景图像重建Transformer")
    print("=" * 50)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建数据加载器
    print("\n📊 准备数据...")
    train_loader, val_loader = create_traffic_data_loaders(
        data_dir='./data/cifar-10-batches-py', 
        batch_size=32, 
        img_size=64
    )
    
    # 可视化样本
    print("\n🖼️ 可视化交通场景样本...")
    visualize_traffic_samples(train_loader)
    
    # 创建模型
    print("\n🏗️ 创建模型...")
    model = SimpleImageTransformer(
        img_size=64, 
        patch_size=8, 
        embed_dim=256, 
        num_layers=6, 
        num_heads=8
    )
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练器
    trainer = TrafficSceneTrainer(model, train_loader, val_loader, device)
    
    # 训练模型
    print("\n🎯 开始训练...")
    trainer.train(num_epochs=50)
    
    # 绘制训练历史
    trainer.plot_training_history()
    
    # 测试重建效果
    print("\n🧪 测试重建效果...")
    test_traffic_reconstruction(model, val_loader, device)
    
    print("\n✅ 交通场景重建完成！")
    print("\n📁 生成的文件:")
    print("- traffic_samples.png: 交通场景样本")
    print("- traffic_training_history.png: 训练历史")
    print("- traffic_reconstruction_results.png: 重建结果")
    print("- best_traffic_model.pth: 最佳模型权重")

if __name__ == "__main__":
    main()
