"""
详细解释reconstruction_head的工作原理
展示从特征到像素的转换过程
"""

import torch
import torch.nn as nn
import matplotlib.pyplot as plt
import numpy as np

class ReconstructionHeadDemo:
    """演示重建头的工作原理"""
    
    def __init__(self, embed_dim=256, patch_size=8, channels=3):
        self.embed_dim = embed_dim
        self.patch_size = patch_size
        self.channels = channels
        self.pixels_per_patch = channels * patch_size * patch_size
        
        print(f"=== 重建头参数 ===")
        print(f"特征维度 (embed_dim): {embed_dim}")
        print(f"Patch大小: {patch_size}x{patch_size}")
        print(f"图像通道数: {channels}")
        print(f"每个patch的像素数: {self.pixels_per_patch}")
        
        # 这就是重建头！
        self.reconstruction_head = nn.Linear(embed_dim, self.pixels_per_patch)
        print(f"\n重建头: Linear({embed_dim} -> {self.pixels_per_patch})")
        
    def demonstrate_conversion(self):
        """演示特征到像素的转换"""
        print(f"\n=== 转换过程演示 ===")
        
        # 1. 模拟Transformer输出的特征
        batch_size = 1
        num_patches = 4  # 假设2x2的patches
        features = torch.randn(batch_size, num_patches, self.embed_dim)
        print(f"1. Transformer特征: {features.shape}")
        print(f"   含义: {batch_size}个样本, {num_patches}个patches, 每个patch {self.embed_dim}维特征")
        
        # 2. 通过重建头转换
        reconstructed_pixels = self.reconstruction_head(features)
        print(f"\n2. 重建头输出: {reconstructed_pixels.shape}")
        print(f"   含义: {batch_size}个样本, {num_patches}个patches, 每个patch {self.pixels_per_patch}个像素值")
        
        # 3. 重塑为图像patch格式
        reshaped = reconstructed_pixels.view(batch_size, num_patches, 
                                           self.channels, self.patch_size, self.patch_size)
        print(f"\n3. 重塑后: {reshaped.shape}")
        print(f"   含义: {batch_size}个样本, {num_patches}个patches, {self.channels}通道, {self.patch_size}x{self.patch_size}像素")
        
        return features, reconstructed_pixels, reshaped
    
    def visualize_patch_reconstruction(self):
        """可视化单个patch的重建过程"""
        print(f"\n=== 单个Patch重建可视化 ===")
        
        # 创建一个简单的测试patch
        original_patch = torch.zeros(3, self.patch_size, self.patch_size)
        original_patch[0, :4, :4] = 1.0  # 红色方块
        original_patch[1, 4:, 4:] = 1.0  # 绿色方块
        
        print(f"原始patch形状: {original_patch.shape}")
        
        # 模拟编码过程（简化）
        flattened = original_patch.flatten()  # [192]
        print(f"展平后: {flattened.shape}")
        
        # 模拟特征表示（随机）
        feature = torch.randn(self.embed_dim)
        print(f"特征表示: {feature.shape}")
        
        # 通过重建头重建
        with torch.no_grad():
            reconstructed_flat = self.reconstruction_head(feature.unsqueeze(0))
            reconstructed_patch = reconstructed_flat.view(self.channels, self.patch_size, self.patch_size)
        
        print(f"重建patch形状: {reconstructed_patch.shape}")
        
        # 可视化
        fig, axes = plt.subplots(1, 2, figsize=(10, 4))
        
        # 原始patch
        axes[0].imshow(original_patch.permute(1, 2, 0).numpy())
        axes[0].set_title('Original Patch')
        axes[0].axis('off')
        
        # 重建patch（随机初始化的权重，所以效果不好）
        reconstructed_clamped = torch.clamp(reconstructed_patch, 0, 1)
        axes[1].imshow(reconstructed_clamped.permute(1, 2, 0).numpy())
        axes[1].set_title('Reconstructed Patch\n(Random Weights)')
        axes[1].axis('off')
        
        plt.tight_layout()
        plt.savefig('patch_reconstruction_demo.png', dpi=150, bbox_inches='tight')
        plt.close()
        print("可视化结果已保存到: patch_reconstruction_demo.png")
        
        return original_patch, reconstructed_patch

def compare_different_reconstruction_heads():
    """比较不同类型的重建头"""
    print(f"\n=== 不同重建头类型比较 ===")
    
    embed_dim = 256
    patch_size = 8
    channels = 3
    pixels_per_patch = channels * patch_size * patch_size
    
    # 1. 简单线性层（我们使用的）
    linear_head = nn.Linear(embed_dim, pixels_per_patch)
    print(f"1. 线性重建头: Linear({embed_dim} -> {pixels_per_patch})")
    print(f"   参数数量: {sum(p.numel() for p in linear_head.parameters()):,}")
    
    # 2. 多层MLP重建头
    mlp_head = nn.Sequential(
        nn.Linear(embed_dim, embed_dim // 2),
        nn.ReLU(),
        nn.Linear(embed_dim // 2, pixels_per_patch)
    )
    print(f"\n2. MLP重建头: {embed_dim} -> {embed_dim//2} -> {pixels_per_patch}")
    print(f"   参数数量: {sum(p.numel() for p in mlp_head.parameters()):,}")
    
    # 3. 卷积重建头
    conv_head = nn.Sequential(
        nn.Linear(embed_dim, 64 * 4 * 4),  # 先变成小的特征图
        nn.Unflatten(1, (64, 4, 4)),      # 重塑为特征图
        nn.ConvTranspose2d(64, 32, 3, stride=1, padding=1),
        nn.ReLU(),
        nn.ConvTranspose2d(32, channels, 3, stride=2, padding=1, output_padding=1)
    )
    print(f"\n3. 卷积重建头: 线性 + 反卷积")
    print(f"   参数数量: {sum(p.numel() for p in conv_head.parameters()):,}")
    
    # 测试输出形状
    test_feature = torch.randn(1, embed_dim)
    
    with torch.no_grad():
        linear_out = linear_head(test_feature)
        mlp_out = mlp_head(test_feature)
        conv_out = conv_head(test_feature)
    
    print(f"\n输出形状比较:")
    print(f"线性头输出: {linear_out.shape}")
    print(f"MLP头输出: {mlp_out.shape}")
    print(f"卷积头输出: {conv_out.shape}")

def explain_why_linear_is_sufficient():
    """解释为什么简单的线性层就足够了"""
    print(f"\n=== 为什么线性层就足够？ ===")
    
    print("1. Transformer已经做了复杂的特征学习")
    print("   - 多头注意力捕获了复杂的空间关系")
    print("   - 多层编码器学习了层次化特征")
    print("   - 重建头只需要做最后的'解码'")
    
    print("\n2. 线性变换的表达能力")
    print("   - 可以学习任意的线性映射")
    print("   - 结合激活函数可以近似任意函数")
    print("   - 对于像素重建任务通常足够")
    
    print("\n3. 计算效率")
    print("   - 参数少，计算快")
    print("   - 避免过拟合")
    print("   - 易于训练和优化")
    
    print("\n4. 实际应用中的选择")
    print("   - 简单任务：线性层")
    print("   - 复杂任务：MLP或卷积层")
    print("   - 高分辨率：卷积重建头")

if __name__ == "__main__":
    print("🔍 重建头(Reconstruction Head)详细解析")
    print("=" * 60)
    
    # 创建演示对象
    demo = ReconstructionHeadDemo(embed_dim=256, patch_size=8, channels=3)
    
    # 演示转换过程
    features, pixels, reshaped = demo.demonstrate_conversion()
    
    # 可视化patch重建
    original, reconstructed = demo.visualize_patch_reconstruction()
    
    # 比较不同重建头
    compare_different_reconstruction_heads()
    
    # 解释设计选择
    explain_why_linear_is_sufficient()
    
    print(f"\n✅ 总结:")
    print(f"reconstruction_head = nn.Linear(embed_dim, 3 * patch_size * patch_size)")
    print(f"这是一个自定义的线性层，用于将Transformer的抽象特征转换回具体像素值")
    print(f"它是图像重建pipeline中的关键组件，负责'解码'特征为可视化的图像")
