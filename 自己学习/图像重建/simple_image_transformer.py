"""
简化版图像重建Transformer
专注于展示输入图像 -> Transformer处理 -> 输出重建图像的完整流程
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
import numpy as np

class SimpleImageTransformer(nn.Module):
    """简化的图像重建Transformer"""
    
    def __init__(self, img_size=64, patch_size=8, embed_dim=256, num_layers=4, num_heads=8):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2
        self.embed_dim = embed_dim
        
        # 图像到patches的转换
        self.patch_embed = nn.Conv2d(3, embed_dim, kernel_size=patch_size, stride=patch_size)
        
        # 位置编码
        self.pos_embed = nn.Parameter(torch.randn(1, self.num_patches, embed_dim))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim,
            nhead=num_heads,
            dim_feedforward=embed_dim * 4,
            dropout=0.1,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 重建头
        self.reconstruction_head = nn.Linear(embed_dim, 3 * patch_size * patch_size)
        
    def forward(self, x):
        """
        Args:
            x: [batch_size, 3, img_size, img_size]
        Returns:
            reconstructed: [batch_size, 3, img_size, img_size]
        """
        batch_size = x.shape[0]
        
        # 1. 图像分块 [batch_size, embed_dim, num_patches_per_side, num_patches_per_side]
        patches = self.patch_embed(x)
        
        # 2. 重塑为序列 [batch_size, num_patches, embed_dim]
        patches = patches.flatten(2).transpose(1, 2)
        
        # 3. 添加位置编码
        patches = patches + self.pos_embed
        
        # 4. Transformer处理
        encoded = self.transformer(patches)
        
        # 5. 重建patches [batch_size, num_patches, patch_dim]
        reconstructed_patches = self.reconstruction_head(encoded)
        
        # 6. 重建图像
        reconstructed = self.patches_to_image(reconstructed_patches, batch_size)
        
        return reconstructed
    
    def patches_to_image(self, patches, batch_size):
        """将patches重建为图像"""
        patches_per_side = self.img_size // self.patch_size
        patch_dim = 3 * self.patch_size * self.patch_size
        
        # 重塑patches [batch_size, patches_per_side, patches_per_side, 3, patch_size, patch_size]
        patches = patches.view(batch_size, patches_per_side, patches_per_side, 
                              3, self.patch_size, self.patch_size)
        
        # 重建图像 [batch_size, 3, img_size, img_size]
        image = patches.permute(0, 3, 1, 4, 2, 5).contiguous()
        image = image.view(batch_size, 3, self.img_size, self.img_size)
        
        return image

def create_simple_test_image(size=64):
    """创建简单的测试图像"""
    img = torch.zeros(1, 3, size, size)
    
    # 红色方块
    img[0, 0, 10:30, 10:30] = 1.0
    
    # 绿色圆形
    center = size // 2
    y, x = torch.meshgrid(torch.arange(size), torch.arange(size), indexing='ij')
    circle_mask = ((y - center)**2 + (x - center)**2) < (size//6)**2
    img[0, 1, circle_mask] = 1.0
    
    # 蓝色条纹
    img[0, 2, 40:50, :] = 1.0
    
    return img

def add_noise_to_image(img, noise_level=0.1):
    """给图像添加噪声"""
    noise = torch.randn_like(img) * noise_level
    noisy_img = torch.clamp(img + noise, 0, 1)
    return noisy_img

def mask_image_patches(img, mask_ratio=0.3, patch_size=8):
    """随机掩盖图像的一些patches"""
    _, _, h, w = img.shape
    patches_per_side = h // patch_size
    num_patches = patches_per_side ** 2
    num_masked = int(num_patches * mask_ratio)
    
    # 创建掩码
    mask_indices = torch.randperm(num_patches)[:num_masked]
    
    masked_img = img.clone()
    for idx in mask_indices:
        i = idx // patches_per_side
        j = idx % patches_per_side
        start_i, end_i = i * patch_size, (i + 1) * patch_size
        start_j, end_j = j * patch_size, (j + 1) * patch_size
        masked_img[:, :, start_i:end_i, start_j:end_j] = 0
    
    return masked_img, mask_indices

def visualize_results(original, input_img, reconstructed, title="Image Reconstruction", save_path=None):
    """可视化重建结果"""
    # 确保数据在[0,1]范围内
    original = torch.clamp(original, 0, 1)
    input_img = torch.clamp(input_img, 0, 1)
    reconstructed = torch.clamp(reconstructed, 0, 1)
    
    fig, axes = plt.subplots(1, 3, figsize=(12, 4))
    
    # 原始图像
    axes[0].imshow(original[0].permute(1, 2, 0).cpu().numpy())
    axes[0].set_title('Original Image')
    axes[0].axis('off')
    
    # 输入图像（可能有噪声或掩码）
    axes[1].imshow(input_img[0].permute(1, 2, 0).cpu().numpy())
    axes[1].set_title('Input Image')
    axes[1].axis('off')
    
    # 重建图像
    axes[2].imshow(reconstructed[0].permute(1, 2, 0).cpu().numpy())
    axes[2].set_title('Reconstructed Image')
    axes[2].axis('off')
    
    plt.suptitle(title)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"结果已保存到: {save_path}")
    else:
        plt.show()
    
    plt.close()

def test_image_reconstruction():
    """测试图像重建"""
    print("🖼️ 测试图像重建Transformer")
    print("=" * 50)
    
    # 创建模型
    model = SimpleImageTransformer(img_size=64, patch_size=8, embed_dim=256, num_layers=4, num_heads=8)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建测试图像
    original_img = create_simple_test_image(64)
    print(f"原始图像shape: {original_img.shape}")
    
    # 测试1: 完美重建（无噪声）
    print("\n1. 测试完美重建...")
    with torch.no_grad():
        perfect_reconstruction = model(original_img)
    
    mse_perfect = F.mse_loss(perfect_reconstruction, original_img).item()
    print(f"完美重建MSE: {mse_perfect:.6f}")
    
    visualize_results(original_img, original_img, perfect_reconstruction, 
                     "Perfect Reconstruction", "perfect_reconstruction.png")
    
    # 测试2: 去噪重建
    print("\n2. 测试去噪重建...")
    noisy_img = add_noise_to_image(original_img, noise_level=0.2)
    
    with torch.no_grad():
        denoised = model(noisy_img)
    
    mse_denoised = F.mse_loss(denoised, original_img).item()
    print(f"去噪重建MSE: {mse_denoised:.6f}")
    
    visualize_results(original_img, noisy_img, denoised, 
                     "Denoising Reconstruction", "denoising_reconstruction.png")
    
    # 测试3: 图像修复
    print("\n3. 测试图像修复...")
    masked_img, mask_indices = mask_image_patches(original_img, mask_ratio=0.4, patch_size=8)
    
    with torch.no_grad():
        inpainted = model(masked_img)
    
    mse_inpainted = F.mse_loss(inpainted, original_img).item()
    print(f"图像修复MSE: {mse_inpainted:.6f}")
    print(f"掩盖了 {len(mask_indices)} 个patches (总共 {64//8 * 64//8} 个)")
    
    visualize_results(original_img, masked_img, inpainted, 
                     "Image Inpainting", "inpainting_reconstruction.png")
    
    return model, original_img, perfect_reconstruction, denoised, inpainted

def demonstrate_training_process():
    """演示训练过程"""
    print("\n🎯 演示训练过程...")
    
    # 创建模型和优化器
    model = SimpleImageTransformer(img_size=64, patch_size=8, embed_dim=128, num_layers=2, num_heads=4)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # 创建训练数据
    original_img = create_simple_test_image(64)
    
    print("训练前的重建效果:")
    with torch.no_grad():
        initial_reconstruction = model(original_img)
        initial_mse = F.mse_loss(initial_reconstruction, original_img).item()
        print(f"初始MSE: {initial_mse:.6f}")
    
    # 简单训练几步
    print("\n开始训练...")
    model.train()
    for epoch in range(50):
        optimizer.zero_grad()
        
        # 添加噪声作为输入
        noisy_input = add_noise_to_image(original_img, noise_level=0.1)
        
        # 前向传播
        reconstruction = model(noisy_input)
        
        # 计算损失
        loss = F.mse_loss(reconstruction, original_img)
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        if (epoch + 1) % 10 == 0:
            print(f"Epoch {epoch+1}/50, Loss: {loss.item():.6f}")
    
    print("\n训练后的重建效果:")
    model.eval()
    with torch.no_grad():
        final_reconstruction = model(original_img)
        final_mse = F.mse_loss(final_reconstruction, original_img).item()
        print(f"最终MSE: {final_mse:.6f}")
        print(f"改进: {initial_mse - final_mse:.6f}")
    
    # 可视化训练效果
    visualize_results(original_img, initial_reconstruction, final_reconstruction,
                     "Training Progress: Before vs After", "training_progress.png")

if __name__ == "__main__":
    print("🚀 开始简化版图像重建Transformer测试...")
    
    # 测试重建功能
    model, original, perfect, denoised, inpainted = test_image_reconstruction()
    
    # 演示训练过程
    demonstrate_training_process()
    
    print("\n✅ 测试完成！")
    print("\n📊 生成的文件:")
    print("- perfect_reconstruction.png: 完美重建效果")
    print("- denoising_reconstruction.png: 去噪效果") 
    print("- inpainting_reconstruction.png: 图像修复效果")
    print("- training_progress.png: 训练前后对比")
    
    print("\n💡 这个例子展示了:")
    print("1. 图像 -> Transformer -> 重建图像的完整流程")
    print("2. Transformer可以学习图像的内在表示")
    print("3. 可以用于去噪、修复等任务")
    print("4. 通过训练可以提高重建质量")
