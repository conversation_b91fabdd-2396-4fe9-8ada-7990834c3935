"""
可训练的图像重建Transformer
解决重建质量差的问题：通过实际训练让模型学会重建图像
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import matplotlib.pyplot as plt
import numpy as np
from torch.utils.data import Dataset, DataLoader

class SimpleImageTransformer(nn.Module):
    """简化的图像重建Transformer"""
    
    def __init__(self, img_size=64, patch_size=8, embed_dim=256, num_layers=4, num_heads=8):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2
        self.embed_dim = embed_dim
        
        # 图像到patches的转换
        self.patch_embed = nn.Conv2d(3, embed_dim, kernel_size=patch_size, stride=patch_size)
        
        # 位置编码
        self.pos_embed = nn.Parameter(torch.randn(1, self.num_patches, embed_dim))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim,
            nhead=num_heads,
            dim_feedforward=embed_dim * 4,
            dropout=0.1,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 重建头 - 这里是关键！
        self.reconstruction_head = nn.Linear(embed_dim, 3 * patch_size * patch_size)
        
    def forward(self, x):
        batch_size = x.shape[0]
        
        # 1. 图像分块
        patches = self.patch_embed(x)
        patches = patches.flatten(2).transpose(1, 2)
        
        # 2. 添加位置编码
        patches = patches + self.pos_embed
        
        # 3. Transformer处理
        encoded = self.transformer(patches)
        
        # 4. 重建patches
        reconstructed_patches = self.reconstruction_head(encoded)
        
        # 5. 重建图像
        reconstructed = self.patches_to_image(reconstructed_patches, batch_size)
        
        return reconstructed
    
    def patches_to_image(self, patches, batch_size):
        patches_per_side = self.img_size // self.patch_size
        
        patches = patches.view(batch_size, patches_per_side, patches_per_side, 
                              3, self.patch_size, self.patch_size)
        
        image = patches.permute(0, 3, 1, 4, 2, 5).contiguous()
        image = image.view(batch_size, 3, self.img_size, self.img_size)
        
        return image

def create_training_data(num_samples=1000, img_size=64):
    """创建训练数据集"""
    images = []
    
    for _ in range(num_samples):
        # 创建简单的几何图形
        img = torch.zeros(3, img_size, img_size)
        
        # 随机颜色和位置
        color = torch.rand(3)
        
        # 随机选择形状
        shape_type = np.random.choice(['square', 'circle', 'line'])
        
        if shape_type == 'square':
            size = np.random.randint(10, 20)
            x = np.random.randint(0, img_size - size)
            y = np.random.randint(0, img_size - size)
            img[:, y:y+size, x:x+size] = color.unsqueeze(-1).unsqueeze(-1)
            
        elif shape_type == 'circle':
            center_x = np.random.randint(15, img_size - 15)
            center_y = np.random.randint(15, img_size - 15)
            radius = np.random.randint(5, 15)
            
            y, x = torch.meshgrid(torch.arange(img_size), torch.arange(img_size), indexing='ij')
            circle_mask = ((y - center_y)**2 + (x - center_x)**2) < radius**2
            img[:, circle_mask] = color.unsqueeze(-1)
            
        elif shape_type == 'line':
            thickness = np.random.randint(2, 5)
            if np.random.choice([True, False]):  # 水平线
                y = np.random.randint(0, img_size - thickness)
                img[:, y:y+thickness, :] = color.unsqueeze(-1).unsqueeze(-1)
            else:  # 垂直线
                x = np.random.randint(0, img_size - thickness)
                img[:, :, x:x+thickness] = color.unsqueeze(-1).unsqueeze(-1)
        
        images.append(img)
    
    return torch.stack(images)

class ImageDataset(Dataset):
    def __init__(self, images):
        self.images = images
    
    def __len__(self):
        return len(self.images)
    
    def __getitem__(self, idx):
        return self.images[idx], self.images[idx]  # 输入和目标相同

def train_model(model, train_loader, num_epochs=50, lr=0.001):
    """训练模型"""
    optimizer = optim.Adam(model.parameters(), lr=lr)
    criterion = nn.MSELoss()
    
    losses = []
    
    print("开始训练...")
    model.train()
    
    for epoch in range(num_epochs):
        epoch_loss = 0
        num_batches = 0
        
        for batch_idx, (data, target) in enumerate(train_loader):
            optimizer.zero_grad()
            
            # 前向传播
            output = model(data)
            
            # 计算损失
            loss = criterion(output, target)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            num_batches += 1
        
        avg_loss = epoch_loss / num_batches
        losses.append(avg_loss)
        
        if (epoch + 1) % 10 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}, Loss: {avg_loss:.6f}")
    
    return losses

def test_reconstruction_quality(model, test_images, save_path="trained_reconstruction.png"):
    """测试重建质量"""
    model.eval()
    
    with torch.no_grad():
        reconstructed = model(test_images)
    
    # 计算MSE
    mse = F.mse_loss(reconstructed, test_images).item()
    
    # 可视化结果
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    
    for i in range(4):
        # 原始图像
        axes[0, i].imshow(test_images[i].permute(1, 2, 0).cpu().numpy())
        axes[0, i].set_title(f'Original {i+1}')
        axes[0, i].axis('off')
        
        # 重建图像
        recon_img = torch.clamp(reconstructed[i], 0, 1)
        axes[1, i].imshow(recon_img.permute(1, 2, 0).cpu().numpy())
        axes[1, i].set_title(f'Reconstructed {i+1}')
        axes[1, i].axis('off')
    
    plt.suptitle(f'Image Reconstruction Results (MSE: {mse:.6f})')
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"重建结果已保存到: {save_path}")
    print(f"重建MSE: {mse:.6f}")
    
    return mse

def demonstrate_training_effect():
    """演示训练效果"""
    print("🚀 演示训练对重建质量的影响")
    print("=" * 50)
    
    # 创建模型
    model = SimpleImageTransformer(img_size=64, patch_size=8, embed_dim=128, num_layers=3, num_heads=4)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练数据
    print("\n创建训练数据...")
    train_images = create_training_data(num_samples=500, img_size=64)
    test_images = create_training_data(num_samples=4, img_size=64)
    
    # 创建数据加载器
    train_dataset = ImageDataset(train_images)
    train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True)
    
    # 测试训练前的重建质量
    print("\n训练前的重建质量:")
    mse_before = test_reconstruction_quality(model, test_images, "before_training.png")
    
    # 训练模型
    print(f"\n开始训练模型...")
    losses = train_model(model, train_loader, num_epochs=100, lr=0.001)
    
    # 测试训练后的重建质量
    print("\n训练后的重建质量:")
    mse_after = test_reconstruction_quality(model, test_images, "after_training.png")
    
    # 绘制训练曲线
    plt.figure(figsize=(10, 6))
    plt.plot(losses)
    plt.title('Training Loss Curve')
    plt.xlabel('Epoch')
    plt.ylabel('MSE Loss')
    plt.grid(True)
    plt.savefig('training_curve.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"\n📊 训练效果对比:")
    print(f"训练前MSE: {mse_before:.6f}")
    print(f"训练后MSE: {mse_after:.6f}")
    print(f"改进程度: {((mse_before - mse_after) / mse_before * 100):.1f}%")
    
    print(f"\n📁 生成的文件:")
    print("- before_training.png: 训练前的重建效果")
    print("- after_training.png: 训练后的重建效果")
    print("- training_curve.png: 训练损失曲线")
    
    return model, losses, mse_before, mse_after

def test_different_tasks(trained_model):
    """测试不同的重建任务"""
    print("\n🎯 测试不同的重建任务...")
    
    # 创建测试图像
    test_img = create_training_data(num_samples=1, img_size=64)[0:1]
    
    # 1. 添加噪声
    noisy_img = test_img + torch.randn_like(test_img) * 0.1
    noisy_img = torch.clamp(noisy_img, 0, 1)
    
    # 2. 部分遮挡
    masked_img = test_img.clone()
    masked_img[:, :, 20:40, 20:40] = 0  # 遮挡中心区域
    
    trained_model.eval()
    with torch.no_grad():
        # 完美重建
        perfect_recon = trained_model(test_img)
        # 去噪
        denoised = trained_model(noisy_img)
        # 修复
        inpainted = trained_model(masked_img)
    
    # 可视化
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    
    images = [test_img[0], noisy_img[0], masked_img[0], test_img[0]]
    reconstructions = [perfect_recon[0], denoised[0], inpainted[0], perfect_recon[0]]
    titles = ['Original', 'Noisy Input', 'Masked Input', 'Reference']
    recon_titles = ['Perfect Recon', 'Denoised', 'Inpainted', 'Perfect Recon']
    
    for i in range(4):
        # 输入
        axes[0, i].imshow(torch.clamp(images[i], 0, 1).permute(1, 2, 0).cpu().numpy())
        axes[0, i].set_title(titles[i])
        axes[0, i].axis('off')
        
        # 重建
        axes[1, i].imshow(torch.clamp(reconstructions[i], 0, 1).permute(1, 2, 0).cpu().numpy())
        axes[1, i].set_title(recon_titles[i])
        axes[1, i].axis('off')
    
    plt.suptitle('Different Reconstruction Tasks')
    plt.tight_layout()
    plt.savefig('different_tasks.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("不同任务的重建结果已保存到: different_tasks.png")

if __name__ == "__main__":
    print("🔧 可训练的图像重建Transformer")
    print("解决重建质量差的问题！")
    print("=" * 60)
    
    # 演示训练效果
    model, losses, mse_before, mse_after = demonstrate_training_effect()
    
    # 测试不同任务
    test_different_tasks(model)
    
    print("\n✅ 训练完成！现在模型真的学会了重建图像！")
    print("\n💡 关键改进:")
    print("1. 创建了实际的训练数据")
    print("2. 实现了完整的训练循环")
    print("3. 模型通过反向传播学习重建")
    print("4. 重建质量显著提升")
