"""
使用nuScenes-mini数据集和SimpleImageTransformer进行真实交通场景重建
基于您的simple_image_transformer.py模型架构
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import matplotlib.pyplot as plt
import numpy as np
import os
from PIL import Image
import glob

# 导入您的Transformer模型
from simple_image_transformer import SimpleImageTransformer

class NuScenesDataset(Dataset):
    """nuScenes-mini数据集加载器"""
    
    def __init__(self, data_dir, transform=None, img_size=128):
        """
        Args:
            data_dir: nuScenes CAM_BACK图像目录
            transform: 图像变换
            img_size: 目标图像尺寸
        """
        self.data_dir = data_dir
        self.transform = transform
        self.img_size = img_size
        
        # 获取所有jpg图像文件
        self.image_paths = glob.glob(os.path.join(data_dir, "*.jpg"))
        self.image_paths.sort()  # 确保顺序一致
        
        print(f"找到 {len(self.image_paths)} 张nuScenes图像")
        
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        # 加载图像
        image_path = self.image_paths[idx]
        image = Image.open(image_path).convert('RGB')
        
        # 应用变换
        if self.transform:
            image = self.transform(image)
        
        # 返回图像作为输入和目标（自监督重建）
        return image, image

def create_nuscenes_data_loaders(data_dir, batch_size=16, img_size=128, train_ratio=0.8):
    """创建nuScenes数据加载器"""
    
    # 数据变换
    transform = transforms.Compose([
        transforms.Resize((img_size, img_size)),  # 调整尺寸
        transforms.ToTensor(),                    # 转换为tensor
        transforms.Normalize((0.485, 0.456, 0.406), (0.229, 0.224, 0.225))  # ImageNet标准化
    ])
    
    # 创建数据集
    dataset = NuScenesDataset(data_dir, transform=transform, img_size=img_size)
    
    # 划分训练集和验证集
    total_size = len(dataset)
    train_size = int(train_ratio * total_size)
    val_size = total_size - train_size
    
    # 创建索引
    indices = list(range(total_size))
    train_indices = indices[:train_size]
    val_indices = indices[train_size:]
    
    # 创建子集
    train_dataset = torch.utils.data.Subset(dataset, train_indices)
    val_dataset = torch.utils.data.Subset(dataset, val_indices)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
    
    print(f"训练集: {len(train_dataset)} 张图像")
    print(f"验证集: {len(val_dataset)} 张图像")
    
    return train_loader, val_loader

def visualize_nuscenes_samples(data_loader, num_samples=8, save_path="nuscenes_samples.png"):
    """可视化nuScenes样本"""
    
    # 获取一批数据
    data_iter = iter(data_loader)
    images, _ = next(data_iter)
    
    # 反归一化显示
    mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
    images_denorm = images * std + mean
    images_denorm = torch.clamp(images_denorm, 0, 1)
    
    fig, axes = plt.subplots(2, 4, figsize=(20, 10))
    axes = axes.flatten()
    
    for i in range(min(num_samples, len(images))):
        img = images_denorm[i].permute(1, 2, 0).cpu().numpy()
        
        axes[i].imshow(img)
        axes[i].set_title(f'nuScenes CAM_BACK {i+1}', fontsize=12)
        axes[i].axis('off')
    
    # 隐藏多余的子图
    for i in range(num_samples, len(axes)):
        axes[i].axis('off')
    
    plt.suptitle('nuScenes-mini CAM_BACK Traffic Scenes', fontsize=16)
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"nuScenes样本已保存到: {save_path}")

class NuScenesTrainer:
    """nuScenes重建训练器"""
    
    def __init__(self, model, train_loader, val_loader, device='cpu'):
        self.model = model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.device = device
        
        # 优化器和损失函数
        self.optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-4)
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(self.optimizer, T_max=50)
        self.criterion = nn.MSELoss()
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.val_psnr = []
    
    def calculate_psnr(self, img1, img2):
        """计算PSNR"""
        mse = torch.mean((img1 - img2) ** 2)
        if mse == 0:
            return float('inf')
        return 20 * torch.log10(1.0 / torch.sqrt(mse))
    
    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        for batch_idx, (data, target) in enumerate(self.train_loader):
            data, target = data.to(self.device), target.to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            output = self.model(data)
            
            # 计算损失
            loss = self.criterion(output, target)
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx % 10 == 0:
                print(f'  Batch {batch_idx}/{len(self.train_loader)}, Loss: {loss.item():.6f}')
        
        return total_loss / num_batches
    
    def validate(self):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        total_psnr = 0
        num_batches = 0
        
        with torch.no_grad():
            for data, target in self.val_loader:
                data, target = data.to(self.device), target.to(self.device)
                
                # 前向传播
                output = self.model(data)
                
                # 计算损失
                loss = self.criterion(output, target)
                total_loss += loss.item()
                
                # 计算PSNR
                psnr = self.calculate_psnr(output, target)
                total_psnr += psnr.item()
                
                num_batches += 1
        
        avg_loss = total_loss / num_batches
        avg_psnr = total_psnr / num_batches
        
        return avg_loss, avg_psnr
    
    def train(self, num_epochs):
        """训练模型"""
        print(f"开始训练nuScenes重建模型，共{num_epochs}个epoch...")
        
        best_psnr = 0
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            
            # 训练
            train_loss = self.train_epoch()
            
            # 验证
            val_loss, val_psnr = self.validate()
            
            # 更新学习率
            self.scheduler.step()
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.val_psnr.append(val_psnr)
            
            print(f"训练损失: {train_loss:.6f}")
            print(f"验证损失: {val_loss:.6f}")
            print(f"验证PSNR: {val_psnr:.2f} dB")
            print(f"学习率: {self.scheduler.get_last_lr()[0]:.6f}")
            
            # 保存最佳模型
            if val_psnr > best_psnr:
                best_psnr = val_psnr
                torch.save(self.model.state_dict(), 'best_nuscenes_model.pth')
                print(f"💾 保存最佳模型 (PSNR: {best_psnr:.2f} dB)")
    
    def plot_training_history(self, save_path="nuscenes_training_history.png"):
        """绘制训练历史"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        
        # 损失曲线
        ax1.plot(self.train_losses, label='训练损失', color='blue', linewidth=2)
        ax1.plot(self.val_losses, label='验证损失', color='red', linewidth=2)
        ax1.set_title('训练和验证损失', fontsize=14)
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # PSNR曲线
        ax2.plot(self.val_psnr, label='验证PSNR', color='green', linewidth=2)
        ax2.set_title('验证PSNR', fontsize=14)
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('PSNR (dB)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        print(f"📊 训练历史已保存到: {save_path}")

def test_nuscenes_reconstruction(model, test_loader, device='cpu', save_path="nuscenes_reconstruction_results.png"):
    """测试nuScenes重建"""
    model.eval()
    
    # 获取测试样本
    data_iter = iter(test_loader)
    images, _ = next(data_iter)
    images = images[:6].to(device)  # 取前6个样本
    
    with torch.no_grad():
        reconstructed = model(images)
    
    # 反归一化
    mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1).to(device)
    std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1).to(device)
    
    images_denorm = images * std + mean
    reconstructed_denorm = reconstructed * std + mean
    
    images_denorm = torch.clamp(images_denorm, 0, 1)
    reconstructed_denorm = torch.clamp(reconstructed_denorm, 0, 1)
    
    # 计算PSNR
    psnr_values = []
    for i in range(len(images)):
        mse = torch.mean((images_denorm[i] - reconstructed_denorm[i]) ** 2)
        psnr = 20 * torch.log10(1.0 / torch.sqrt(mse))
        psnr_values.append(psnr.item())
    
    # 可视化
    fig, axes = plt.subplots(2, 6, figsize=(24, 8))
    
    for i in range(6):
        # 原始图像
        img = images_denorm[i].permute(1, 2, 0).cpu().numpy()
        axes[0, i].imshow(img)
        axes[0, i].set_title(f'Original {i+1}', fontsize=12)
        axes[0, i].axis('off')
        
        # 重建图像
        recon_img = reconstructed_denorm[i].permute(1, 2, 0).cpu().numpy()
        axes[1, i].imshow(recon_img)
        axes[1, i].set_title(f'Reconstructed {i+1}\nPSNR: {psnr_values[i]:.1f}dB', fontsize=12)
        axes[1, i].axis('off')
    
    plt.suptitle('nuScenes Traffic Scene Reconstruction Results', fontsize=16)
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"🎯 重建结果已保存到: {save_path}")
    print(f"平均PSNR: {np.mean(psnr_values):.2f} dB")

def main():
    """主函数"""
    print("🚗 nuScenes-mini交通场景图像重建")
    print("=" * 60)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 数据目录
    data_dir = "nuscenes-mini/samples/CAM_BACK"
    
    # 创建数据加载器
    print(f"\n📊 加载nuScenes数据: {data_dir}")
    train_loader, val_loader = create_nuscenes_data_loaders(
        data_dir=data_dir,
        batch_size=8,  # 真实图像较大，使用较小batch size
        img_size=128,  # 使用128x128分辨率
        train_ratio=0.8
    )
    
    # 可视化样本
    print("\n🖼️ 可视化nuScenes样本...")
    visualize_nuscenes_samples(train_loader)
    
    # 创建模型
    print("\n🏗️ 创建SimpleImageTransformer模型...")
    model = SimpleImageTransformer(
        img_size=128,     # 匹配数据尺寸
        patch_size=16,    # 使用16x16 patches
        embed_dim=384,    # 增加嵌入维度
        num_layers=8,     # 增加层数处理复杂场景
        num_heads=12      # 增加注意力头数
    )
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练器并训练
    print("\n🎯 开始训练...")
    trainer = NuScenesTrainer(model, train_loader, val_loader, device)
    trainer.train(num_epochs=30)
    
    # 绘制训练历史
    trainer.plot_training_history()
    
    # 测试重建效果
    print("\n🧪 测试重建效果...")
    test_nuscenes_reconstruction(model, val_loader, device)
    
    print("\n✅ nuScenes交通场景重建完成！")
    print("📁 生成的文件:")
    print("- nuscenes_samples.png: nuScenes样本")
    print("- nuscenes_training_history.png: 训练历史")
    print("- nuscenes_reconstruction_results.png: 重建结果")
    print("- best_nuscenes_model.pth: 最佳模型权重")

if __name__ == "__main__":
    main()
