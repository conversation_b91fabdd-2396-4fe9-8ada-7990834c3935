"""
Transformer可视化和分析工具
帮助深入理解Transformer各个组件的工作原理

功能：
1. 注意力权重可视化
2. 位置编码分析
3. 梯度流动分析
4. 层间特征变化可视化
5. 掩码机制演示
"""

import torch
import torch.nn as nn
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from complete_transformer import *

class TransformerAnalyzer:
    """Transformer分析器"""
    
    def __init__(self, model, tokenizer=None):
        self.model = model
        self.tokenizer = tokenizer
        self.attention_weights = {}
        self.layer_outputs = {}
        
    def register_hooks(self):
        """注册钩子函数来捕获中间结果"""
        def get_attention_hook(name):
            def hook(module, input_data, output):
                # 忽略未使用的参数
                _ = module, input_data
                if isinstance(output, tuple) and len(output) >= 2:
                    self.attention_weights[name] = output[1].detach()
            return hook

        def get_layer_hook(name):
            def hook(module, input_data, output):
                # 忽略未使用的参数
                _ = module, input_data
                if isinstance(output, tuple):
                    self.layer_outputs[name] = output[0].detach()
                else:
                    self.layer_outputs[name] = output.detach()
            return hook
        
        # 为编码器层注册钩子
        if hasattr(self.model, 'encoder'):
            for i, layer in enumerate(self.model.encoder.layers):
                layer.self_attention.register_forward_hook(
                    get_attention_hook(f'encoder_layer_{i}_self_attn'))
                layer.register_forward_hook(
                    get_layer_hook(f'encoder_layer_{i}_output'))
        
        # 为解码器层注册钩子
        if hasattr(self.model, 'decoder'):
            for i, layer in enumerate(self.model.decoder.layers):
                layer.self_attention.register_forward_hook(
                    get_attention_hook(f'decoder_layer_{i}_self_attn'))
                layer.cross_attention.register_forward_hook(
                    get_attention_hook(f'decoder_layer_{i}_cross_attn'))
                layer.register_forward_hook(
                    get_layer_hook(f'decoder_layer_{i}_output'))

    def visualize_attention_patterns(self, layer_idx=0, head_idx=0, attention_type='encoder_self'):
        """可视化注意力模式"""
        if attention_type == 'encoder_self':
            key = f'encoder_layer_{layer_idx}_self_attn'
        elif attention_type == 'decoder_self':
            key = f'decoder_layer_{layer_idx}_self_attn'
        elif attention_type == 'decoder_cross':
            key = f'decoder_layer_{layer_idx}_cross_attn'
        else:
            raise ValueError("attention_type must be 'encoder_self', 'decoder_self', or 'decoder_cross'")
        
        if key not in self.attention_weights:
            print(f"注意力权重 {key} 不存在，请先运行模型")
            return
        
        # 获取注意力权重 [batch_size, n_heads, seq_len, seq_len]
        attn_weights = self.attention_weights[key][0, head_idx].cpu().numpy()
        
        plt.figure(figsize=(12, 10))
        
        # 主要的注意力热图
        plt.subplot(2, 2, 1)
        sns.heatmap(attn_weights, cmap='Blues', cbar=True, square=True)
        plt.title(f'{attention_type.replace("_", " ").title()} - Layer {layer_idx}, Head {head_idx}')
        plt.xlabel('Key Position')
        plt.ylabel('Query Position')
        
        # 注意力分布统计
        plt.subplot(2, 2, 2)
        plt.hist(attn_weights.flatten(), bins=50, alpha=0.7, color='skyblue')
        plt.title('Attention Weight Distribution')
        plt.xlabel('Attention Weight')
        plt.ylabel('Frequency')
        
        # 每个查询位置的注意力熵
        plt.subplot(2, 2, 3)
        entropy = -np.sum(attn_weights * np.log(attn_weights + 1e-9), axis=1)
        plt.plot(entropy, marker='o')
        plt.title('Attention Entropy per Query Position')
        plt.xlabel('Query Position')
        plt.ylabel('Entropy')
        
        # 注意力权重的最大值位置
        plt.subplot(2, 2, 4)
        max_positions = np.argmax(attn_weights, axis=1)
        plt.plot(max_positions, marker='s', color='red')
        plt.title('Position of Maximum Attention per Query')
        plt.xlabel('Query Position')
        plt.ylabel('Key Position with Max Attention')
        
        plt.tight_layout()
        plt.show()

    def visualize_positional_encoding(self, d_model=512, max_len=100):
        """可视化位置编码"""
        pe = PositionalEncoding(d_model, max_len)
        pos_encoding = pe.pe[0, :max_len, :].numpy()
        
        _, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 位置编码热图
        im1 = axes[0, 0].imshow(pos_encoding.T, cmap='RdYlBu', aspect='auto')
        axes[0, 0].set_title('Positional Encoding Heatmap')
        axes[0, 0].set_xlabel('Position')
        axes[0, 0].set_ylabel('Dimension')
        plt.colorbar(im1, ax=axes[0, 0])
        
        # 特定维度的位置编码曲线
        dims_to_plot = [0, 1, d_model//4, d_model//2, d_model-1]
        for dim in dims_to_plot:
            axes[0, 1].plot(pos_encoding[:, dim], label=f'dim {dim}')
        axes[0, 1].set_title('Positional Encoding for Different Dimensions')
        axes[0, 1].set_xlabel('Position')
        axes[0, 1].set_ylabel('Value')
        axes[0, 1].legend()
        
        # 相邻位置的相似性
        similarities = []
        for i in range(max_len - 1):
            sim = np.dot(pos_encoding[i], pos_encoding[i+1]) / (
                np.linalg.norm(pos_encoding[i]) * np.linalg.norm(pos_encoding[i+1]))
            similarities.append(sim)
        
        axes[1, 0].plot(similarities)
        axes[1, 0].set_title('Cosine Similarity Between Adjacent Positions')
        axes[1, 0].set_xlabel('Position')
        axes[1, 0].set_ylabel('Cosine Similarity')
        
        # 位置编码的频率分析
        freqs = np.fft.fftfreq(d_model, 1.0)
        fft_result = np.abs(np.fft.fft(pos_encoding[10]))  # 分析第10个位置
        axes[1, 1].plot(freqs[:d_model//2], fft_result[:d_model//2])
        axes[1, 1].set_title('Frequency Analysis of Position 10')
        axes[1, 1].set_xlabel('Frequency')
        axes[1, 1].set_ylabel('Magnitude')
        
        plt.tight_layout()
        plt.show()

    def analyze_layer_representations(self):
        """分析各层的表示变化"""
        if not self.layer_outputs:
            print("没有捕获到层输出，请先运行模型")
            return
        
        layer_names = sorted([k for k in self.layer_outputs.keys() if 'output' in k])
        
        plt.figure(figsize=(15, 10))
        
        # 计算每层输出的统计信息
        layer_stats = {}
        for name in layer_names:
            output = self.layer_outputs[name][0]  # 取第一个样本
            layer_stats[name] = {
                'mean': output.mean().item(),
                'std': output.std().item(),
                'max': output.max().item(),
                'min': output.min().item(),
                'norm': torch.norm(output, dim=-1).mean().item()
            }
        
        # 绘制统计信息
        stats_to_plot = ['mean', 'std', 'norm']
        for i, stat in enumerate(stats_to_plot):
            plt.subplot(2, 3, i+1)
            values = [layer_stats[name][stat] for name in layer_names]
            plt.plot(values, marker='o')
            plt.title(f'Layer-wise {stat.title()}')
            plt.xlabel('Layer')
            plt.ylabel(stat.title())
            plt.xticks(range(len(layer_names)), [name.split('_')[2] for name in layer_names], rotation=45)
        
        # 表示相似性分析
        plt.subplot(2, 3, 4)
        similarities = []
        for i in range(len(layer_names) - 1):
            out1 = self.layer_outputs[layer_names[i]][0].flatten()
            out2 = self.layer_outputs[layer_names[i+1]][0].flatten()
            sim = torch.cosine_similarity(out1, out2, dim=0).item()
            similarities.append(sim)
        
        plt.plot(similarities, marker='s', color='red')
        plt.title('Cosine Similarity Between Adjacent Layers')
        plt.xlabel('Layer Transition')
        plt.ylabel('Cosine Similarity')
        
        # 维度方差分析
        plt.subplot(2, 3, 5)
        for name in layer_names[:3]:  # 只显示前3层
            output = self.layer_outputs[name][0]
            dim_vars = torch.var(output, dim=0).cpu().numpy()
            plt.plot(dim_vars, alpha=0.7, label=name.split('_')[2])
        plt.title('Dimension-wise Variance (First 3 Layers)')
        plt.xlabel('Dimension')
        plt.ylabel('Variance')
        plt.legend()
        
        # 激活分布
        plt.subplot(2, 3, 6)
        for i, name in enumerate(layer_names[::2]):  # 每隔一层
            output = self.layer_outputs[name][0].flatten().cpu().numpy()
            plt.hist(output, bins=50, alpha=0.5, label=f'Layer {name.split("_")[2]}')
        plt.title('Activation Distribution Across Layers')
        plt.xlabel('Activation Value')
        plt.ylabel('Frequency')
        plt.legend()
        
        plt.tight_layout()
        plt.show()

    def demonstrate_masking_effects(self):
        """演示掩码机制的效果"""
        print("=== 掩码机制演示 ===")
        
        # 创建示例序列
        seq_len = 8
        
        # 1. 填充掩码
        print("1. 填充掩码效果：")
        seq_with_padding = torch.tensor([[1, 2, 3, 4, 0, 0, 0, 0]])  # 后4个是填充
        padding_mask = create_padding_mask(seq_with_padding, pad_token=0)
        
        plt.figure(figsize=(12, 4))
        
        plt.subplot(1, 3, 1)
        plt.imshow(seq_with_padding.numpy(), cmap='viridis', aspect='auto')
        plt.title('Sequence with Padding')
        plt.xlabel('Position')
        plt.ylabel('Batch')
        
        plt.subplot(1, 3, 2)
        plt.imshow(padding_mask[0, 0].numpy(), cmap='RdYlBu', aspect='auto')
        plt.title('Padding Mask')
        plt.xlabel('Position')
        plt.ylabel('Attention to Position')
        
        # 2. 前瞻掩码
        plt.subplot(1, 3, 3)
        look_ahead_mask = create_look_ahead_mask(seq_len)
        plt.imshow(look_ahead_mask.numpy(), cmap='RdYlBu', aspect='auto')
        plt.title('Look-ahead Mask')
        plt.xlabel('Key Position')
        plt.ylabel('Query Position')
        
        plt.tight_layout()
        plt.show()
        
        print("填充掩码：防止注意力关注填充位置")
        print("前瞻掩码：防止解码器看到未来信息")

def run_comprehensive_analysis():
    """运行完整的Transformer分析"""
    print("🔍 开始Transformer深度分析...")
    
    # 创建模型
    model = Transformer(src_vocab_size=1000, tgt_vocab_size=800, d_model=256, n_heads=8, 
                       num_encoder_layers=4, num_decoder_layers=4, d_ff=1024)
    
    # 创建分析器
    analyzer = TransformerAnalyzer(model)
    analyzer.register_hooks()
    
    # 创建测试数据
    src = torch.randint(1, 1000, (2, 10))
    tgt = torch.randint(1, 800, (2, 8))
    
    # 运行模型以捕获中间结果
    with torch.no_grad():
        _ = model(src, tgt)
    
    print("\n1. 位置编码分析...")
    analyzer.visualize_positional_encoding(d_model=256, max_len=50)
    
    print("\n2. 注意力模式分析...")
    analyzer.visualize_attention_patterns(layer_idx=0, head_idx=0, attention_type='encoder_self')
    
    print("\n3. 层间表示分析...")
    analyzer.analyze_layer_representations()
    
    print("\n4. 掩码机制演示...")
    analyzer.demonstrate_masking_effects()
    
    print("\n✅ 分析完成！")

if __name__ == "__main__":
    run_comprehensive_analysis()
