"""
Transformer入门代码 - 第一步：使用现成的模型
目标：理解输入输出格式，观察模型结构
"""

import torch
import torch.nn as nn
from transformers import AutoModel, AutoTokenizer, AutoConfig
import matplotlib.pyplot as plt
import numpy as np

# 1. 加载预训练模型和分词器
print("=== 第1步：加载预训练模型 ===")
model_name = "bert-base-uncased"
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModel.from_pretrained(model_name)

# 查看模型结构
print(f"模型配置：{model.config}")
print(f"模型参数数量：{sum(p.numel() for p in model.parameters()):,}")

# 2. 准备输入数据
print("\n=== 第2步：准备输入数据 ===")
texts = [
    "Hello, how are you?",
    "I love machine learning!",
    "Transformer is powerful."
]

# 分词和编码
encoded = tokenizer(texts, padding=True, truncation=True, return_tensors="pt")
print(f"输入token IDs shape: {encoded['input_ids'].shape}")
print(f"注意力掩码shape: {encoded['attention_mask'].shape}")

# 查看分词结果
for i, text in enumerate(texts):
    tokens = tokenizer.tokenize(text)
    print(f"文本 {i+1}: {text}")
    print(f"分词结果: {tokens}")
    print(f"token IDs: {encoded['input_ids'][i]}")
    print()

# 3. 前向传播
print("=== 第3步：前向传播 ===")
with torch.no_grad():
    outputs = model(**encoded)

print(f"输出特征shape: {outputs.last_hidden_state.shape}")
print(f"池化输出shape: {outputs.pooler_output.shape}")

# 4. 可视化注意力权重
print("\n=== 第4步：可视化注意力权重 ===")
# 注意：需要输出注意力权重
model_with_attention = AutoModel.from_pretrained(model_name, output_attentions=True)

with torch.no_grad():
    outputs_with_attention = model_with_attention(**encoded)

# 获取第一个样本的注意力权重
attentions = outputs_with_attention.attentions  # 12层的注意力权重
first_layer_attention = attentions[0][0]  # 第0层，第0个样本
print(f"第一层注意力权重shape: {first_layer_attention.shape}")  # [12, seq_len, seq_len]

# 可视化函数
def visualize_attention(attention_weights, tokens, layer=0, head=0):
    """可视化注意力权重"""
    plt.figure(figsize=(10, 8))
    
    # 取特定层和头的注意力权重
    attn = attention_weights[layer][0, head].cpu().numpy()  # [seq_len, seq_len]
    
    # 创建热力图
    plt.imshow(attn, cmap='Blues', aspect='auto')
    plt.colorbar()
    
    # 添加标签
    plt.xticks(range(len(tokens)), tokens, rotation=45)
    plt.yticks(range(len(tokens)), tokens)
    plt.xlabel('Keys')
    plt.ylabel('Queries')
    plt.title(f'Attention Weights - Layer {layer}, Head {head}')
    plt.tight_layout()
    plt.show()

# 可视化第一个句子的注意力
first_text_tokens = tokenizer.tokenize(tokenizer.decode(encoded['input_ids'][0]))
first_text_tokens = ['[CLS]'] + first_text_tokens + ['[SEP]']  # 添加特殊token
visualize_attention(attentions, first_text_tokens, layer=0, head=0)

# 5. 特征提取示例
print("\n=== 第5步：特征提取示例 ===")
def extract_features(text):
    """提取文本特征"""
    encoded = tokenizer(text, return_tensors="pt")
    with torch.no_grad():
        outputs = model(**encoded)
    
    # 获取[CLS]token的特征（通常用于分类任务）
    cls_features = outputs.last_hidden_state[0, 0]  # 第一个token是[CLS]
    
    # 获取句子的平均特征
    attention_mask = encoded['attention_mask'][0]
    token_features = outputs.last_hidden_state[0]  # [seq_len, hidden_size]
    
    # 只计算有效token的平均值
    valid_tokens = attention_mask.unsqueeze(-1).expand_as(token_features)
    masked_features = token_features * valid_tokens
    avg_features = masked_features.sum(dim=0) / attention_mask.sum()
    
    return cls_features, avg_features

# 测试特征提取
test_text = "Transformer models are amazing!"
cls_feat, avg_feat = extract_features(test_text)
print(f"CLS特征维度: {cls_feat.shape}")
print(f"平均特征维度: {avg_feat.shape}")

# 6. 简单的相似度计算
print("\n=== 第6步：计算句子相似度 ===")
def sentence_similarity(text1, text2):
    """计算两个句子的相似度"""
    _, feat1 = extract_features(text1)
    _, feat2 = extract_features(text2)
    
    # 计算余弦相似度
    similarity = torch.cosine_similarity(feat1, feat2, dim=0)
    return similarity.item()

# 测试相似度
sentences = [
    "I love machine learning",
    "Machine learning is great",
    "I hate rainy days"
]

print("句子相似度矩阵：")
for i, sent1 in enumerate(sentences):
    for j, sent2 in enumerate(sentences):
        sim = sentence_similarity(sent1, sent2)
        print(f"句子{i+1} vs 句子{j+1}: {sim:.3f}")

print("\n=== 完成！===")
print("你已经成功运行了第一个Transformer例子！")
print("下一步可以尝试：")
print("1. 修改输入文本，观察注意力权重变化")
print("2. 尝试不同的预训练模型")
print("3. 理解每个模块的作用")