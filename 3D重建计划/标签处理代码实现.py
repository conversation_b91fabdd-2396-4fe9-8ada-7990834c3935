#!/usr/bin/env python3
"""
V2X语义通信3D重建 - 标签处理代码实现
包含OPV2V、V2X-Real、DAIR-V2X三个数据集的标签处理
"""

import torch
import torch.nn as nn
import numpy as np
import cv2
import json
import os
from pathlib import Path
import open3d as o3d
from typing import Dict, List, Tuple, Optional

# ============================================================================
# 1. OPV2V标签处理器（直接使用现有标签）
# ============================================================================

class OPV2VLabelProcessor:
    """OPV2V数据集标签处理器 - 标签完美，直接使用"""
    
    def __init__(self, data_root: str):
        self.data_root = Path(data_root)
        self.image_size = (224, 224)
        
    def load_scene_labels(self, scenario_id: str) -> Dict:
        """加载OPV2V场景的所有标签"""
        scenario_path = self.data_root / scenario_id
        
        labels = {
            'vehicles': [],
            'communication_graph': {},
            'ground_truth_3d': None
        }
        
        # 加载通信图
        comm_graph_path = scenario_path / "communication_graph.json"
        if comm_graph_path.exists():
            with open(comm_graph_path, 'r') as f:
                labels['communication_graph'] = json.load(f)
        
        # 加载每辆车的标签
        for agent_dir in scenario_path.glob("agent_*"):
            agent_id = int(agent_dir.name.split('_')[1])
            vehicle_labels = self.load_vehicle_labels(agent_dir)
            labels['vehicles'].append(vehicle_labels)
        
        # 合并所有车辆的LiDAR作为全局ground truth
        labels['ground_truth_3d'] = self.merge_vehicle_lidars(labels['vehicles'])
        
        return labels
    
    def load_vehicle_labels(self, agent_path: Path) -> Dict:
        """加载单车的标签信息"""
        
        # 1. 加载车辆位姿
        pose_file = agent_path / "pose.txt"
        pose_matrix = self.load_pose_matrix(pose_file)
        
        # 2. 加载LiDAR点云
        lidar_file = agent_path / "lidar" / "000000.pcd"
        lidar_points = self.load_lidar_points(lidar_file)
        
        # 3. 加载相机参数
        camera_params = self.load_camera_parameters(agent_path)
        
        # 4. 生成深度图（从LiDAR投影）
        depth_maps = self.generate_depth_maps(lidar_points, camera_params)
        
        # 5. 加载3D目标检测标签（如果存在）
        objects_3d = self.load_3d_objects(agent_path)
        
        return {
            'pose': pose_matrix,
            'lidar_points': lidar_points,
            'camera_params': camera_params,
            'depth_maps': depth_maps,
            'objects_3d': objects_3d,
            'agent_id': int(agent_path.name.split('_')[1])
        }
    
    def load_pose_matrix(self, pose_file: Path) -> np.ndarray:
        """加载车辆位姿矩阵"""
        if not pose_file.exists():
            return np.eye(4)
            
        with open(pose_file, 'r') as f:
            pose_data = f.readline().strip().split()
            
        # 解析位置和旋转
        x, y, z = float(pose_data[0]), float(pose_data[1]), float(pose_data[2])
        roll, pitch, yaw = float(pose_data[3]), float(pose_data[4]), float(pose_data[5])
        
        # 构建4x4变换矩阵
        pose_matrix = np.eye(4)
        pose_matrix[:3, 3] = [x, y, z]
        
        # 旋转矩阵（ZYX欧拉角）
        R = self.euler_to_rotation_matrix(roll, pitch, yaw)
        pose_matrix[:3, :3] = R
        
        return pose_matrix
    
    def load_lidar_points(self, lidar_file: Path) -> np.ndarray:
        """加载LiDAR点云"""
        if not lidar_file.exists():
            return np.empty((0, 3))
            
        # 使用open3d加载点云
        pcd = o3d.io.read_point_cloud(str(lidar_file))
        points = np.asarray(pcd.points)
        
        return points
    
    def load_camera_parameters(self, agent_path: Path) -> Dict:
        """加载相机参数"""
        # OPV2V的相机参数通常在配置文件中
        camera_params = {}
        
        # 标准的相机配置（根据OPV2V文档）
        cameras = ['rgb_front', 'rgb_left', 'rgb_right', 'rgb_rear']
        
        for i, camera_name in enumerate(cameras):
            camera_params[camera_name] = {
                'intrinsic': np.array([
                    [800.0, 0.0, 400.0],
                    [0.0, 800.0, 300.0],
                    [0.0, 0.0, 1.0]
                ]),
                'extrinsic': self.get_camera_extrinsic(camera_name),
                'distortion': np.zeros(5)
            }
        
        return camera_params
    
    def get_camera_extrinsic(self, camera_name: str) -> np.ndarray:
        """获取相机外参（相对于车辆坐标系）"""
        extrinsics = {
            'rgb_front': np.array([
                [1, 0, 0, 0],
                [0, 1, 0, 0],
                [0, 0, 1, 1.5],
                [0, 0, 0, 1]
            ]),
            'rgb_left': np.array([
                [0, -1, 0, 0],
                [1, 0, 0, 0],
                [0, 0, 1, 1.5],
                [0, 0, 0, 1]
            ]),
            'rgb_right': np.array([
                [0, 1, 0, 0],
                [-1, 0, 0, 0],
                [0, 0, 1, 1.5],
                [0, 0, 0, 1]
            ]),
            'rgb_rear': np.array([
                [-1, 0, 0, 0],
                [0, -1, 0, 0],
                [0, 0, 1, 1.5],
                [0, 0, 0, 1]
            ])
        }
        return extrinsics.get(camera_name, np.eye(4))
    
    def generate_depth_maps(self, lidar_points: np.ndarray, camera_params: Dict) -> Dict:
        """从LiDAR点云生成深度图"""
        depth_maps = {}
        
        for camera_name, params in camera_params.items():
            # 将LiDAR点投影到相机图像平面
            depth_map = self.project_lidar_to_image(
                lidar_points,
                params['intrinsic'],
                params['extrinsic'],
                self.image_size
            )
            depth_maps[camera_name] = depth_map
        
        return depth_maps
    
    def project_lidar_to_image(self, points: np.ndarray, K: np.ndarray, 
                              T: np.ndarray, image_size: Tuple[int, int]) -> np.ndarray:
        """将LiDAR点投影到图像平面生成深度图"""
        if len(points) == 0:
            return np.zeros(image_size)
        
        # 转换到相机坐标系
        points_homo = np.hstack([points, np.ones((len(points), 1))])
        points_cam = (T @ points_homo.T).T[:, :3]
        
        # 过滤掉相机后方的点
        valid_mask = points_cam[:, 2] > 0
        points_cam = points_cam[valid_mask]
        
        if len(points_cam) == 0:
            return np.zeros(image_size)
        
        # 投影到图像平面
        points_2d = (K @ points_cam.T).T
        points_2d = points_2d[:, :2] / points_2d[:, 2:3]
        
        # 创建深度图
        depth_map = np.zeros(image_size)
        
        for i, (u, v) in enumerate(points_2d):
            u, v = int(u), int(v)
            if 0 <= u < image_size[1] and 0 <= v < image_size[0]:
                depth = points_cam[i, 2]
                if depth_map[v, u] == 0 or depth < depth_map[v, u]:
                    depth_map[v, u] = depth
        
        return depth_map
    
    def load_3d_objects(self, agent_path: Path) -> List[Dict]:
        """加载3D目标检测标签"""
        objects_file = agent_path / "objects_3d.json"
        if not objects_file.exists():
            return []
        
        with open(objects_file, 'r') as f:
            objects_data = json.load(f)
        
        return objects_data.get('objects', [])
    
    def merge_vehicle_lidars(self, vehicles_labels: List[Dict]) -> np.ndarray:
        """合并所有车辆的LiDAR点云作为全局ground truth"""
        all_points = []
        
        for vehicle_label in vehicles_labels:
            points = vehicle_label['lidar_points']
            pose = vehicle_label['pose']
            
            # 转换到全局坐标系
            if len(points) > 0:
                points_homo = np.hstack([points, np.ones((len(points), 1))])
                global_points = (pose @ points_homo.T).T[:, :3]
                all_points.append(global_points)
        
        if all_points:
            return np.vstack(all_points)
        else:
            return np.empty((0, 3))
    
    @staticmethod
    def euler_to_rotation_matrix(roll: float, pitch: float, yaw: float) -> np.ndarray:
        """欧拉角转旋转矩阵"""
        R_x = np.array([
            [1, 0, 0],
            [0, np.cos(roll), -np.sin(roll)],
            [0, np.sin(roll), np.cos(roll)]
        ])
        
        R_y = np.array([
            [np.cos(pitch), 0, np.sin(pitch)],
            [0, 1, 0],
            [-np.sin(pitch), 0, np.cos(pitch)]
        ])
        
        R_z = np.array([
            [np.cos(yaw), -np.sin(yaw), 0],
            [np.sin(yaw), np.cos(yaw), 0],
            [0, 0, 1]
        ])
        
        return R_z @ R_y @ R_x

# ============================================================================
# 使用示例
# ============================================================================

def test_opv2v_label_processor():
    """测试OPV2V标签处理器"""
    
    # 初始化处理器
    processor = OPV2VLabelProcessor("/path/to/opv2v/data")
    
    # 加载场景标签
    scenario_id = "scenario_001"
    labels = processor.load_scene_labels(scenario_id)
    
    print("=== OPV2V标签处理结果 ===")
    print(f"车辆数量: {len(labels['vehicles'])}")
    print(f"全局3D点云: {labels['ground_truth_3d'].shape}")
    print(f"通信图: {labels['communication_graph']}")
    
    # 检查每辆车的标签
    for i, vehicle in enumerate(labels['vehicles']):
        print(f"\n车辆 {i}:")
        print(f"  位姿矩阵: {vehicle['pose'].shape}")
        print(f"  LiDAR点数: {len(vehicle['lidar_points'])}")
        print(f"  深度图数量: {len(vehicle['depth_maps'])}")
        print(f"  3D目标数: {len(vehicle['objects_3d'])}")

# ============================================================================
# 2. V2X-Real标签处理器（自动生成标签）
# ============================================================================

class V2XRealLabelProcessor:
    """V2X-Real数据集标签处理器 - 自动生成缺失标签"""

    def __init__(self, data_root: str):
        self.data_root = Path(data_root)
        self.setup_auto_generators()

    def setup_auto_generators(self):
        """设置自动标签生成器"""
        try:
            # 单目深度估计模型
            self.depth_estimator = torch.hub.load('intel-isl/MiDaS', 'MiDaS')
            self.depth_transform = torch.hub.load('intel-isl/MiDaS', 'transforms').dpt_transform
            print("✅ MiDaS深度估计模型加载成功")
        except:
            print("⚠️ MiDaS模型加载失败，将使用简单深度估计")
            self.depth_estimator = None

        # SLAM系统（简化版）
        self.slam_initialized = False

    def process_v2x_real_scene(self, scene_id: str) -> Dict:
        """处理V2X-Real场景，生成缺失标签"""
        scene_path = self.data_root / scene_id

        # 1. 加载原始数据
        raw_data = self.load_raw_v2x_data(scene_path)

        # 2. 生成缺失标签
        generated_labels = self.generate_missing_labels(raw_data)

        # 3. 合并原始数据和生成标签
        complete_labels = {
            **raw_data,
            **generated_labels,
            'label_quality': self.assess_label_quality(generated_labels)
        }

        return complete_labels

    def load_raw_v2x_data(self, scene_path: Path) -> Dict:
        """加载V2X-Real原始数据"""

        # 加载GPS数据
        gps_file = scene_path / "gps_data.txt"
        gps_data = self.load_gps_data(gps_file)

        # 加载IMU数据
        imu_file = scene_path / "imu_data.txt"
        imu_data = self.load_imu_data(imu_file)

        # 加载通信日志
        comm_file = scene_path / "communication_log.json"
        comm_log = self.load_communication_log(comm_file)

        # 加载图像
        images = self.load_synchronized_images(scene_path)

        return {
            'gps_data': gps_data,
            'imu_data': imu_data,
            'communication_log': comm_log,
            'images': images,
            'timestamps': self.extract_timestamps(scene_path)
        }

    def generate_missing_labels(self, raw_data: Dict) -> Dict:
        """生成缺失的标签"""

        # 1. 从GPS/IMU计算车辆位姿
        vehicle_poses = self.compute_vehicle_poses(
            raw_data['gps_data'],
            raw_data['imu_data']
        )

        # 2. 生成深度图
        depth_maps = self.generate_depth_maps_auto(raw_data['images'])

        # 3. SLAM重建生成3D点云
        ground_truth_3d = self.slam_reconstruction(
            raw_data['images'],
            vehicle_poses
        )

        # 4. 计算跨视图对应关系
        correspondences = self.find_correspondences(raw_data['images'])

        return {
            'vehicle_poses': vehicle_poses,
            'depth_maps': depth_maps,
            'ground_truth_3d': ground_truth_3d,
            'correspondences': correspondences
        }

    def load_gps_data(self, gps_file: Path) -> List[Dict]:
        """加载GPS数据"""
        if not gps_file.exists():
            return []

        gps_data = []
        with open(gps_file, 'r') as f:
            for line in f:
                parts = line.strip().split(',')
                if len(parts) >= 4:
                    gps_data.append({
                        'timestamp': float(parts[0]),
                        'latitude': float(parts[1]),
                        'longitude': float(parts[2]),
                        'altitude': float(parts[3])
                    })
        return gps_data

    def load_imu_data(self, imu_file: Path) -> List[Dict]:
        """加载IMU数据"""
        if not imu_file.exists():
            return []

        imu_data = []
        with open(imu_file, 'r') as f:
            for line in f:
                parts = line.strip().split(',')
                if len(parts) >= 7:
                    imu_data.append({
                        'timestamp': float(parts[0]),
                        'accel_x': float(parts[1]),
                        'accel_y': float(parts[2]),
                        'accel_z': float(parts[3]),
                        'gyro_x': float(parts[4]),
                        'gyro_y': float(parts[5]),
                        'gyro_z': float(parts[6])
                    })
        return imu_data

    def compute_vehicle_poses(self, gps_data: List[Dict], imu_data: List[Dict]) -> List[np.ndarray]:
        """从GPS/IMU计算车辆位姿"""
        poses = []

        if not gps_data:
            return poses

        # 简化的位姿计算（实际应用中需要更复杂的融合算法）
        for i, gps_point in enumerate(gps_data):
            # GPS坐标转换为局部坐标
            if i == 0:
                # 第一个点作为原点
                origin_lat, origin_lon = gps_point['latitude'], gps_point['longitude']
                x, y = 0.0, 0.0
            else:
                # 简单的经纬度转米（近似）
                x = (gps_point['longitude'] - origin_lon) * 111320 * np.cos(np.radians(origin_lat))
                y = (gps_point['latitude'] - origin_lat) * 110540

            z = gps_point['altitude']

            # 从IMU获取姿态（如果有对应时间戳的数据）
            yaw = self.get_yaw_from_imu(imu_data, gps_point['timestamp'])

            # 构建位姿矩阵
            pose = np.eye(4)
            pose[0, 3] = x
            pose[1, 3] = y
            pose[2, 3] = z

            # 简单的yaw旋转
            pose[0, 0] = np.cos(yaw)
            pose[0, 1] = -np.sin(yaw)
            pose[1, 0] = np.sin(yaw)
            pose[1, 1] = np.cos(yaw)

            poses.append(pose)

        return poses

    def get_yaw_from_imu(self, imu_data: List[Dict], timestamp: float) -> float:
        """从IMU数据获取偏航角"""
        if not imu_data:
            return 0.0

        # 找到最接近的时间戳
        closest_imu = min(imu_data, key=lambda x: abs(x['timestamp'] - timestamp))

        # 简化的偏航角计算（实际需要积分陀螺仪数据）
        return np.arctan2(closest_imu['gyro_z'], closest_imu['gyro_x'])

    def generate_depth_maps_auto(self, images: List[np.ndarray]) -> List[np.ndarray]:
        """自动生成深度图"""
        depth_maps = []

        for image in images:
            if self.depth_estimator is not None:
                # 使用MiDaS生成深度图
                depth = self.estimate_depth_midas(image)
            else:
                # 简单的深度估计（基于图像梯度）
                depth = self.estimate_depth_simple(image)

            depth_maps.append(depth)

        return depth_maps

    def estimate_depth_midas(self, image: np.ndarray) -> np.ndarray:
        """使用MiDaS估计深度"""
        try:
            # 预处理图像
            input_tensor = self.depth_transform(image).unsqueeze(0)

            # 深度估计
            with torch.no_grad():
                depth = self.depth_estimator(input_tensor)
                depth = torch.nn.functional.interpolate(
                    depth.unsqueeze(1),
                    size=image.shape[:2],
                    mode="bicubic",
                    align_corners=False,
                ).squeeze()

            return depth.cpu().numpy()
        except Exception as e:
            print(f"MiDaS深度估计失败: {e}")
            return self.estimate_depth_simple(image)

    def estimate_depth_simple(self, image: np.ndarray) -> np.ndarray:
        """简单的深度估计（基于图像梯度）"""
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)

        # 计算梯度
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

        # 简单的深度映射（梯度大的地方深度小）
        depth = 1.0 / (gradient_magnitude + 1e-6)
        depth = (depth - depth.min()) / (depth.max() - depth.min()) * 100  # 归一化到0-100米

        return depth

    def slam_reconstruction(self, images: List[np.ndarray], poses: List[np.ndarray]) -> np.ndarray:
        """简化的SLAM重建"""
        # 这里实现一个简化版的SLAM
        # 实际应用中应该使用ORB-SLAM3等成熟系统

        if len(images) < 2:
            return np.empty((0, 3))

        # 使用特征匹配进行简单的三角化
        points_3d = []

        for i in range(len(images) - 1):
            img1, img2 = images[i], images[i + 1]
            pose1, pose2 = poses[i], poses[i + 1]

            # 特征检测和匹配
            matches = self.match_features(img1, img2)

            # 三角化
            triangulated = self.triangulate_points(matches, pose1, pose2)
            points_3d.append(triangulated)

        if points_3d:
            return np.vstack(points_3d)
        else:
            return np.empty((0, 3))

    def match_features(self, img1: np.ndarray, img2: np.ndarray) -> List[Tuple]:
        """特征匹配"""
        # 使用ORB特征检测器
        orb = cv2.ORB_create()

        # 检测关键点和描述子
        kp1, des1 = orb.detectAndCompute(img1, None)
        kp2, des2 = orb.detectAndCompute(img2, None)

        if des1 is None or des2 is None:
            return []

        # 特征匹配
        bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
        matches = bf.match(des1, des2)

        # 提取匹配点坐标
        matched_points = []
        for match in matches:
            pt1 = kp1[match.queryIdx].pt
            pt2 = kp2[match.trainIdx].pt
            matched_points.append((pt1, pt2))

        return matched_points

    def triangulate_points(self, matches: List[Tuple], pose1: np.ndarray, pose2: np.ndarray) -> np.ndarray:
        """三角化重建3D点"""
        if len(matches) < 4:
            return np.empty((0, 3))

        # 简化的相机内参（实际应该从标定获得）
        K = np.array([
            [800, 0, 400],
            [0, 800, 300],
            [0, 0, 1]
        ], dtype=np.float32)

        # 提取匹配点
        pts1 = np.array([match[0] for match in matches], dtype=np.float32)
        pts2 = np.array([match[1] for match in matches], dtype=np.float32)

        # 计算投影矩阵
        P1 = K @ pose1[:3, :]
        P2 = K @ pose2[:3, :]

        # 三角化
        points_4d = cv2.triangulatePoints(P1, P2, pts1.T, pts2.T)
        points_3d = points_4d[:3, :] / points_4d[3, :]

        return points_3d.T

    def find_correspondences(self, images: List[np.ndarray]) -> Dict:
        """计算跨视图对应关系"""
        correspondences = {}

        for i in range(len(images)):
            for j in range(i + 1, len(images)):
                matches = self.match_features(images[i], images[j])
                correspondences[f'view_{i}_to_view_{j}'] = matches

        return correspondences

    def assess_label_quality(self, labels: Dict) -> Dict:
        """评估生成标签的质量"""
        quality = {
            'pose_quality': 'medium',  # GPS/IMU融合质量中等
            'depth_quality': 'good' if self.depth_estimator else 'low',
            '3d_reconstruction_quality': 'medium',  # 简化SLAM质量中等
            'correspondence_quality': 'good'  # 特征匹配质量较好
        }

        return quality

# ============================================================================
# 3. DAIR-V2X标签处理器（使用现有丰富标签）
# ============================================================================

class DAIRV2XLabelProcessor:
    """DAIR-V2X数据集标签处理器 - 使用现有丰富标签"""

    def __init__(self, data_root: str):
        self.data_root = Path(data_root)

    def process_dair_v2x_scene(self, scene_id: str) -> Dict:
        """处理DAIR-V2X场景标签"""
        scene_path = self.data_root / scene_id

        # 1. 加载车端数据
        vehicle_data = self.load_vehicle_side_data(scene_path / "vehicle-side")

        # 2. 加载路侧数据
        infrastructure_data = self.load_infrastructure_side_data(scene_path / "infrastructure-side")

        # 3. 加载协同标注
        cooperative_labels = self.load_cooperative_labels(scene_path / "cooperative")

        # 4. 适配为V2V格式（将基础设施视为虚拟车辆）
        adapted_labels = self.adapt_v2i_to_v2v_format(
            vehicle_data, infrastructure_data, cooperative_labels
        )

        return adapted_labels

    def load_vehicle_side_data(self, vehicle_path: Path) -> Dict:
        """加载车端数据"""

        # 加载车载相机图像
        camera_files = list(vehicle_path.glob("image/*.jpg"))
        images = [cv2.imread(str(f)) for f in sorted(camera_files)]

        # 加载车载LiDAR
        lidar_files = list(vehicle_path.glob("velodyne/*.pcd"))
        lidar_points = []
        for lidar_file in sorted(lidar_files):
            pcd = o3d.io.read_point_cloud(str(lidar_file))
            lidar_points.append(np.asarray(pcd.points))

        # 加载车辆位姿
        pose_file = vehicle_path / "calib" / "lidar_to_novatel.json"
        vehicle_pose = self.load_pose_from_calib(pose_file)

        # 加载相机标定
        camera_calib = self.load_camera_calibration(vehicle_path / "calib")

        return {
            'images': images,
            'lidar_points': lidar_points,
            'pose': vehicle_pose,
            'camera_calibration': camera_calib,
            'type': 'vehicle'
        }

    def load_infrastructure_side_data(self, infra_path: Path) -> Dict:
        """加载路侧设备数据"""

        # 加载路侧相机图像
        camera_files = list(infra_path.glob("image/*.jpg"))
        images = [cv2.imread(str(f)) for f in sorted(camera_files)]

        # 加载路侧LiDAR
        lidar_files = list(infra_path.glob("velodyne/*.pcd"))
        lidar_points = []
        for lidar_file in sorted(lidar_files):
            pcd = o3d.io.read_point_cloud(str(lidar_file))
            lidar_points.append(np.asarray(pcd.points))

        # 加载基础设施位姿
        pose_file = infra_path / "calib" / "virtuallidar_to_world.json"
        infra_pose = self.load_pose_from_calib(pose_file)

        # 加载相机标定
        camera_calib = self.load_camera_calibration(infra_path / "calib")

        return {
            'images': images,
            'lidar_points': lidar_points,
            'pose': infra_pose,
            'camera_calibration': camera_calib,
            'type': 'infrastructure'
        }

    def load_cooperative_labels(self, coop_path: Path) -> Dict:
        """加载协同标注"""

        # 加载协同3D目标检测标注
        label_file = coop_path / "label_world" / "000000.json"
        cooperative_objects = self.load_cooperative_objects(label_file)

        return {
            'cooperative_objects': cooperative_objects,
            'fusion_strategy': 'early_fusion'  # DAIR-V2X使用早期融合
        }

    def load_pose_from_calib(self, calib_file: Path) -> np.ndarray:
        """从标定文件加载位姿"""
        if not calib_file.exists():
            return np.eye(4)

        with open(calib_file, 'r') as f:
            calib_data = json.load(f)

        # 提取旋转和平移
        if 'transform' in calib_data:
            transform = calib_data['transform']
            rotation = transform.get('rotation', [0, 0, 0])
            translation = transform.get('translation', [0, 0, 0])
        else:
            rotation = [0, 0, 0]
            translation = [0, 0, 0]

        # 构建位姿矩阵
        pose = np.eye(4)
        pose[:3, 3] = translation

        # 旋转矩阵（假设是欧拉角）
        R = OPV2VLabelProcessor.euler_to_rotation_matrix(*rotation)
        pose[:3, :3] = R

        return pose

    def load_camera_calibration(self, calib_path: Path) -> Dict:
        """加载相机标定参数"""
        calib_file = calib_path / "camera_intrinsic.json"

        if not calib_file.exists():
            # 默认相机参数
            return {
                'intrinsic': np.array([
                    [1200, 0, 960],
                    [0, 1200, 540],
                    [0, 0, 1]
                ]),
                'distortion': np.zeros(5)
            }

        with open(calib_file, 'r') as f:
            calib_data = json.load(f)

        # 提取内参矩阵
        if 'cam_K' in calib_data:
            K = np.array(calib_data['cam_K']).reshape(3, 3)
        else:
            K = np.array([
                [1200, 0, 960],
                [0, 1200, 540],
                [0, 0, 1]
            ])

        return {
            'intrinsic': K,
            'distortion': np.array(calib_data.get('cam_D', [0, 0, 0, 0, 0]))
        }

    def load_cooperative_objects(self, label_file: Path) -> List[Dict]:
        """加载协同3D目标标注"""
        if not label_file.exists():
            return []

        with open(label_file, 'r') as f:
            label_data = json.load(f)

        objects = []
        for obj in label_data.get('objects', []):
            objects.append({
                'class': obj.get('type', 'unknown'),
                'bbox_3d': obj.get('3d_location', {}).get('bbox', [0, 0, 0, 1, 1, 1, 0]),
                'confidence': obj.get('confidence', 1.0),
                'source': obj.get('source', 'cooperative')
            })

        return objects

    def adapt_v2i_to_v2v_format(self, vehicle_data: Dict, infra_data: Dict, coop_labels: Dict) -> Dict:
        """将V2I数据适配为V2V格式"""

        # 将基础设施视为一个特殊的"车辆"
        adapted_vehicles = [
            {
                'images': vehicle_data['images'],
                'lidar_points': vehicle_data['lidar_points'],
                'pose': vehicle_data['pose'],
                'camera_calibration': vehicle_data['camera_calibration'],
                'agent_id': 0,
                'type': 'vehicle'
            },
            {
                'images': infra_data['images'],
                'lidar_points': infra_data['lidar_points'],
                'pose': infra_data['pose'],
                'camera_calibration': infra_data['camera_calibration'],
                'agent_id': 1,
                'type': 'infrastructure'  # 标记为基础设施
            }
        ]

        # 构建通信图（车辆和基础设施总是连通的）
        communication_graph = {
            'adjacency_matrix': np.array([
                [0, 1],  # 车辆到基础设施
                [1, 0]   # 基础设施到车辆
            ]),
            'communication_range': 1000.0,  # 基础设施通信范围大
            'signal_strength': np.array([
                [0, 1.0],
                [1.0, 0]
            ])
        }

        # 合并协同标注作为ground truth
        ground_truth_3d = self.merge_cooperative_objects(coop_labels['cooperative_objects'])

        return {
            'vehicles': adapted_vehicles,
            'communication_graph': communication_graph,
            'ground_truth_3d': ground_truth_3d,
            'cooperative_labels': coop_labels,
            'dataset_type': 'dair_v2x_adapted'
        }

    def merge_cooperative_objects(self, objects: List[Dict]) -> np.ndarray:
        """将协同目标转换为3D点云"""
        points = []

        for obj in objects:
            bbox = obj['bbox_3d']
            if len(bbox) >= 7:  # x, y, z, l, w, h, yaw
                # 从边界框生成点云（简化）
                x, y, z, l, w, h, yaw = bbox[:7]

                # 生成边界框的8个顶点
                corners = self.generate_bbox_corners(x, y, z, l, w, h, yaw)
                points.append(corners)

        if points:
            return np.vstack(points)
        else:
            return np.empty((0, 3))

    def generate_bbox_corners(self, x: float, y: float, z: float,
                            l: float, w: float, h: float, yaw: float) -> np.ndarray:
        """生成3D边界框的8个顶点"""
        # 边界框的8个顶点（局部坐标）
        corners = np.array([
            [-l/2, -w/2, -h/2],
            [l/2, -w/2, -h/2],
            [l/2, w/2, -h/2],
            [-l/2, w/2, -h/2],
            [-l/2, -w/2, h/2],
            [l/2, -w/2, h/2],
            [l/2, w/2, h/2],
            [-l/2, w/2, h/2]
        ])

        # 旋转
        R = np.array([
            [np.cos(yaw), -np.sin(yaw), 0],
            [np.sin(yaw), np.cos(yaw), 0],
            [0, 0, 1]
        ])

        rotated_corners = (R @ corners.T).T

        # 平移
        rotated_corners[:, 0] += x
        rotated_corners[:, 1] += y
        rotated_corners[:, 2] += z

        return rotated_corners

# ============================================================================
# 4. 统一标签处理接口
# ============================================================================

class UnifiedLabelProcessor:
    """统一的标签处理接口，支持所有三个数据集"""

    def __init__(self, dataset_type: str, data_root: str):
        self.dataset_type = dataset_type
        self.data_root = data_root

        # 初始化对应的处理器
        if dataset_type == 'opv2v':
            self.processor = OPV2VLabelProcessor(data_root)
        elif dataset_type == 'v2x_real':
            self.processor = V2XRealLabelProcessor(data_root)
        elif dataset_type == 'dair_v2x':
            self.processor = DAIRV2XLabelProcessor(data_root)
        else:
            raise ValueError(f"不支持的数据集类型: {dataset_type}")

    def process_scene(self, scene_id: str) -> Dict:
        """处理场景标签（统一接口）"""
        if self.dataset_type == 'opv2v':
            return self.processor.load_scene_labels(scene_id)
        elif self.dataset_type == 'v2x_real':
            return self.processor.process_v2x_real_scene(scene_id)
        elif self.dataset_type == 'dair_v2x':
            return self.processor.process_dair_v2x_scene(scene_id)

    def get_label_quality_report(self, scene_labels: Dict) -> Dict:
        """获取标签质量报告"""
        report = {
            'dataset_type': self.dataset_type,
            'num_vehicles': len(scene_labels.get('vehicles', [])),
            'has_ground_truth_3d': 'ground_truth_3d' in scene_labels,
            'has_communication_graph': 'communication_graph' in scene_labels
        }

        if self.dataset_type == 'opv2v':
            report['label_quality'] = 'perfect'
            report['manual_annotation_needed'] = False
        elif self.dataset_type == 'v2x_real':
            report['label_quality'] = scene_labels.get('label_quality', {})
            report['manual_annotation_needed'] = False  # 自动生成
        elif self.dataset_type == 'dair_v2x':
            report['label_quality'] = 'high'
            report['manual_annotation_needed'] = False

        return report

# ============================================================================
# 5. 测试和使用示例
# ============================================================================

def test_all_label_processors():
    """测试所有标签处理器"""

    print("=== 测试标签处理器 ===\n")

    # 测试数据路径（需要根据实际情况修改）
    test_paths = {
        'opv2v': "/path/to/opv2v/data",
        'v2x_real': "/path/to/v2x_real/data",
        'dair_v2x': "/path/to/dair_v2x/data"
    }

    for dataset_type, data_path in test_paths.items():
        print(f"--- 测试 {dataset_type.upper()} ---")

        try:
            # 创建统一处理器
            processor = UnifiedLabelProcessor(dataset_type, data_path)

            # 处理测试场景
            scene_id = "test_scene_001"
            labels = processor.process_scene(scene_id)

            # 获取质量报告
            quality_report = processor.get_label_quality_report(labels)

            print(f"✅ {dataset_type} 处理成功")
            print(f"   车辆数量: {quality_report['num_vehicles']}")
            print(f"   标签质量: {quality_report['label_quality']}")
            print(f"   需要手动标注: {quality_report['manual_annotation_needed']}")

            if 'ground_truth_3d' in labels:
                gt_shape = labels['ground_truth_3d'].shape
                print(f"   3D点云: {gt_shape}")

        except Exception as e:
            print(f"❌ {dataset_type} 处理失败: {e}")

        print()

def create_label_processing_pipeline():
    """创建标签处理流水线"""

    class LabelProcessingPipeline:
        def __init__(self):
            self.processors = {}

        def add_dataset(self, dataset_type: str, data_root: str):
            """添加数据集处理器"""
            self.processors[dataset_type] = UnifiedLabelProcessor(dataset_type, data_root)

        def process_all_scenes(self, scene_ids: Dict[str, List[str]]) -> Dict:
            """批量处理所有场景"""
            results = {}

            for dataset_type, scenes in scene_ids.items():
                if dataset_type not in self.processors:
                    print(f"警告: 未找到 {dataset_type} 的处理器")
                    continue

                dataset_results = []
                processor = self.processors[dataset_type]

                for scene_id in scenes:
                    try:
                        labels = processor.process_scene(scene_id)
                        quality = processor.get_label_quality_report(labels)

                        dataset_results.append({
                            'scene_id': scene_id,
                            'labels': labels,
                            'quality': quality
                        })

                    except Exception as e:
                        print(f"处理 {dataset_type}/{scene_id} 失败: {e}")

                results[dataset_type] = dataset_results

            return results

        def generate_summary_report(self, results: Dict) -> Dict:
            """生成汇总报告"""
            summary = {
                'total_datasets': len(results),
                'total_scenes': sum(len(scenes) for scenes in results.values()),
                'dataset_details': {}
            }

            for dataset_type, scenes in results.items():
                successful_scenes = [s for s in scenes if 'labels' in s]

                summary['dataset_details'][dataset_type] = {
                    'total_scenes': len(scenes),
                    'successful_scenes': len(successful_scenes),
                    'success_rate': len(successful_scenes) / len(scenes) if scenes else 0,
                    'manual_annotation_needed': any(
                        s['quality'].get('manual_annotation_needed', False)
                        for s in successful_scenes
                    )
                }

            return summary

    return LabelProcessingPipeline()

if __name__ == "__main__":
    # 运行测试
    test_all_label_processors()

    # 创建处理流水线示例
    pipeline = create_label_processing_pipeline()

    # 添加数据集
    pipeline.add_dataset('opv2v', '/path/to/opv2v')
    pipeline.add_dataset('v2x_real', '/path/to/v2x_real')
    pipeline.add_dataset('dair_v2x', '/path/to/dair_v2x')

    # 批量处理
    scene_ids = {
        'opv2v': ['scene_001', 'scene_002'],
        'v2x_real': ['real_scene_001'],
        'dair_v2x': ['dair_scene_001', 'dair_scene_002']
    }

    results = pipeline.process_all_scenes(scene_ids)
    summary = pipeline.generate_summary_report(results)

    print("=== 批量处理汇总 ===")
    print(f"总数据集: {summary['total_datasets']}")
    print(f"总场景数: {summary['total_scenes']}")

    for dataset, details in summary['dataset_details'].items():
        print(f"{dataset}: {details['successful_scenes']}/{details['total_scenes']} 成功")
        print(f"  需要手动标注: {details['manual_annotation_needed']}")
