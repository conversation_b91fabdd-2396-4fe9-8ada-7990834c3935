#!/usr/bin/env python3
"""
V2X语义通信3D重建 - 标签处理快速开始脚本
这个脚本帮助您快速验证和处理各数据集的标签
"""

import torch
import numpy as np
import cv2
import json
import os
from pathlib import Path

# ============================================================================
# 快速验证脚本
# ============================================================================

def quick_verify_opv2v(data_root: str):
    """快速验证OPV2V数据集标签"""
    print("=== 验证OPV2V数据集 ===")
    
    data_path = Path(data_root)
    if not data_path.exists():
        print(f"❌ 数据路径不存在: {data_root}")
        return False
    
    # 查找第一个场景
    scenarios = list(data_path.glob("scenario_*"))
    if not scenarios:
        print("❌ 未找到场景数据")
        return False
    
    first_scenario = scenarios[0]
    print(f"✅ 找到场景: {first_scenario.name}")
    
    # 检查车辆数据
    agents = list(first_scenario.glob("agent_*"))
    print(f"✅ 车辆数量: {len(agents)}")
    
    # 检查第一辆车的数据
    if agents:
        agent_path = agents[0]
        
        # 检查图像
        rgb_dirs = ['rgb_front', 'rgb_left', 'rgb_right', 'rgb_rear']
        available_views = []
        for rgb_dir in rgb_dirs:
            if (agent_path / rgb_dir).exists():
                images = list((agent_path / rgb_dir).glob("*.png"))
                if images:
                    available_views.append(rgb_dir)
        
        print(f"✅ 可用视图: {available_views}")
        
        # 检查LiDAR
        lidar_dir = agent_path / "lidar"
        if lidar_dir.exists():
            lidar_files = list(lidar_dir.glob("*.pcd"))
            print(f"✅ LiDAR文件: {len(lidar_files)}")
        else:
            print("⚠️ 未找到LiDAR数据")
        
        # 检查位姿
        pose_file = agent_path / "pose.txt"
        if pose_file.exists():
            print("✅ 位姿文件存在")
        else:
            print("⚠️ 未找到位姿文件")
    
    # 检查通信图
    comm_file = first_scenario / "communication_graph.json"
    if comm_file.exists():
        print("✅ 通信图文件存在")
    else:
        print("⚠️ 未找到通信图文件")
    
    return True

def quick_setup_auto_labeling():
    """快速设置自动标签生成"""
    print("=== 设置自动标签生成 ===")
    
    try:
        # 测试MiDaS深度估计
        print("正在下载MiDaS模型...")
        model = torch.hub.load('intel-isl/MiDaS', 'MiDaS_small')
        transform = torch.hub.load('intel-isl/MiDaS', 'transforms').small_transform
        print("✅ MiDaS模型加载成功")
        
        # 测试深度估计
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        input_tensor = transform(test_image).unsqueeze(0)
        
        with torch.no_grad():
            depth = model(input_tensor)
            print(f"✅ 深度估计测试成功，输出形状: {depth.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 自动标签生成设置失败: {e}")
        print("💡 建议：检查网络连接，或使用离线模式")
        return False

def create_minimal_dataset_loader():
    """创建最小化的数据集加载器"""
    
    class MinimalOPV2VLoader:
        def __init__(self, data_root: str):
            self.data_root = Path(data_root)
        
        def load_scene(self, scenario_id: str):
            """加载一个场景的基本数据"""
            scenario_path = self.data_root / scenario_id
            
            if not scenario_path.exists():
                raise FileNotFoundError(f"场景不存在: {scenario_id}")
            
            # 加载车辆数据
            vehicles = []
            for agent_dir in scenario_path.glob("agent_*"):
                vehicle_data = self.load_vehicle_basic(agent_dir)
                vehicles.append(vehicle_data)
            
            return {
                'scenario_id': scenario_id,
                'vehicles': vehicles,
                'num_vehicles': len(vehicles)
            }
        
        def load_vehicle_basic(self, agent_path: Path):
            """加载车辆基本数据"""
            agent_id = int(agent_path.name.split('_')[1])
            
            # 加载一张前视图像作为示例
            front_img_dir = agent_path / "rgb_front"
            if front_img_dir.exists():
                img_files = list(front_img_dir.glob("*.png"))
                if img_files:
                    image = cv2.imread(str(img_files[0]))
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                else:
                    image = None
            else:
                image = None
            
            # 加载位姿（简化版）
            pose_file = agent_path / "pose.txt"
            if pose_file.exists():
                with open(pose_file, 'r') as f:
                    pose_data = f.readline().strip().split()
                    if len(pose_data) >= 6:
                        x, y, z = float(pose_data[0]), float(pose_data[1]), float(pose_data[2])
                        yaw = float(pose_data[5])
                        pose = {'x': x, 'y': y, 'z': z, 'yaw': yaw}
                    else:
                        pose = {'x': 0, 'y': 0, 'z': 0, 'yaw': 0}
            else:
                pose = {'x': 0, 'y': 0, 'z': 0, 'yaw': 0}
            
            return {
                'agent_id': agent_id,
                'image': image,
                'pose': pose,
                'has_data': image is not None
            }
    
    return MinimalOPV2VLoader

def test_minimal_loading(data_root: str):
    """测试最小化加载"""
    print("=== 测试最小化数据加载 ===")
    
    try:
        loader = create_minimal_dataset_loader()(data_root)
        
        # 查找第一个场景
        data_path = Path(data_root)
        scenarios = list(data_path.glob("scenario_*"))
        
        if not scenarios:
            print("❌ 未找到场景数据")
            return False
        
        # 加载第一个场景
        scene_data = loader.load_scene(scenarios[0].name)
        
        print(f"✅ 成功加载场景: {scene_data['scenario_id']}")
        print(f"✅ 车辆数量: {scene_data['num_vehicles']}")
        
        # 检查每辆车的数据
        for vehicle in scene_data['vehicles']:
            status = "有数据" if vehicle['has_data'] else "无数据"
            print(f"   车辆 {vehicle['agent_id']}: {status}")
            print(f"     位置: ({vehicle['pose']['x']:.1f}, {vehicle['pose']['y']:.1f})")
            if vehicle['image'] is not None:
                print(f"     图像尺寸: {vehicle['image'].shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 最小化加载失败: {e}")
        return False

def generate_quick_start_config():
    """生成快速开始配置文件"""
    config = {
        "datasets": {
            "opv2v": {
                "data_root": "/path/to/opv2v/data",
                "enabled": True,
                "label_processing": "direct_use",
                "manual_annotation_needed": False
            },
            "v2x_real": {
                "data_root": "/path/to/v2x_real/data", 
                "enabled": False,
                "label_processing": "auto_generate",
                "manual_annotation_needed": False
            },
            "dair_v2x": {
                "data_root": "/path/to/dair_v2x/data",
                "enabled": False,
                "label_processing": "format_adaptation",
                "manual_annotation_needed": False
            }
        },
        "auto_labeling": {
            "depth_estimation": {
                "method": "midas",
                "model_size": "small",
                "enabled": True
            },
            "slam_reconstruction": {
                "method": "simple_triangulation",
                "enabled": True
            }
        },
        "output": {
            "processed_labels_dir": "./processed_labels",
            "quality_reports_dir": "./quality_reports"
        }
    }
    
    config_file = "label_processing_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 配置文件已生成: {config_file}")
    print("💡 请修改配置文件中的数据路径")
    
    return config_file

def main():
    """主函数 - 快速开始流程"""
    print("🚀 V2X语义通信3D重建 - 标签处理快速开始")
    print("=" * 50)
    
    # 步骤1：生成配置文件
    print("\n📋 步骤1: 生成配置文件")
    config_file = generate_quick_start_config()
    
    # 步骤2：设置自动标签生成
    print("\n🔧 步骤2: 设置自动标签生成")
    auto_setup_success = quick_setup_auto_labeling()
    
    # 步骤3：验证OPV2V数据（如果路径存在）
    print("\n✅ 步骤3: 验证数据集")
    
    # 读取配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    opv2v_path = config['datasets']['opv2v']['data_root']
    if opv2v_path != "/path/to/opv2v/data" and Path(opv2v_path).exists():
        opv2v_success = quick_verify_opv2v(opv2v_path)
        if opv2v_success:
            test_minimal_loading(opv2v_path)
    else:
        print("⚠️ OPV2V数据路径未设置或不存在，请修改配置文件")
    
    # 步骤4：总结
    print("\n📊 快速开始总结")
    print("=" * 30)
    print(f"✅ 配置文件: {config_file}")
    print(f"{'✅' if auto_setup_success else '❌'} 自动标签生成: {'就绪' if auto_setup_success else '需要手动设置'}")
    
    print("\n🎯 下一步行动:")
    print("1. 修改配置文件中的数据路径")
    print("2. 运行完整的标签处理脚本")
    print("3. 开始算法开发")
    
    print("\n💡 提示:")
    print("- OPV2V: 标签完美，可直接使用")
    print("- V2X-Real: 需要自动生成标签")
    print("- DAIR-V2X: 需要格式适配")

if __name__ == "__main__":
    main()
