"""
基于本地生成交通场景数据的图像重建Transformer
不依赖外部数据集下载，使用程序生成的交通场景数据

生成的交通场景包括：
- 道路背景
- 车辆形状
- 交通标志
- 建筑物轮廓
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
import numpy as np
import random
from PIL import Image, ImageDraw
import math

# 导入我们之前的Transformer模型
from simple_image_transformer import SimpleImageTransformer

class SyntheticTrafficDataset(Dataset):
    """合成交通场景数据集"""
    
    def __init__(self, num_samples=1000, img_size=64):
        self.num_samples = num_samples
        self.img_size = img_size
        
    def __len__(self):
        return self.num_samples
    
    def generate_road(self, img):
        """生成道路"""
        draw = ImageDraw.Draw(img)
        
        # 道路背景 - 灰色
        road_color = (100, 100, 100)
        draw.rectangle([0, self.img_size//2, self.img_size, self.img_size], fill=road_color)
        
        # 道路标线 - 白色虚线
        line_color = (255, 255, 255)
        y_center = self.img_size * 3 // 4
        for x in range(0, self.img_size, 8):
            if x % 16 < 8:  # 虚线效果
                draw.rectangle([x, y_center-1, x+4, y_center+1], fill=line_color)
        
        return img
    
    def generate_car(self, img, x, y, color):
        """生成汽车"""
        draw = ImageDraw.Draw(img)
        
        # 车身
        car_width, car_height = 16, 8
        draw.rectangle([x, y, x+car_width, y+car_height], fill=color)
        
        # 车窗 - 浅蓝色
        window_color = (173, 216, 230)
        draw.rectangle([x+2, y+1, x+car_width-2, y+3], fill=window_color)
        
        # 车轮 - 黑色
        wheel_color = (0, 0, 0)
        draw.ellipse([x+2, y+car_height-2, x+5, y+car_height+1], fill=wheel_color)
        draw.ellipse([x+car_width-5, y+car_height-2, x+car_width-2, y+car_height+1], fill=wheel_color)
        
        return img
    
    def generate_building(self, img, x, y, width, height, color):
        """生成建筑物"""
        draw = ImageDraw.Draw(img)
        
        # 建筑主体
        draw.rectangle([x, y, x+width, y+height], fill=color)
        
        # 窗户
        window_color = (255, 255, 0)  # 黄色窗户
        for i in range(2, width-2, 6):
            for j in range(2, height-2, 8):
                draw.rectangle([x+i, y+j, x+i+3, y+j+4], fill=window_color)
        
        return img
    
    def generate_traffic_sign(self, img, x, y):
        """生成交通标志"""
        draw = ImageDraw.Draw(img)
        
        # 标志杆
        pole_color = (128, 128, 128)
        draw.rectangle([x+4, y, x+6, y+20], fill=pole_color)
        
        # 标志牌 - 红色圆形
        sign_color = (255, 0, 0)
        draw.ellipse([x, y-8, x+10, y+2], fill=sign_color)
        
        # 标志内容 - 白色
        content_color = (255, 255, 255)
        draw.rectangle([x+3, y-5, x+7, y-1], fill=content_color)
        
        return img
    
    def generate_traffic_scene(self):
        """生成一个完整的交通场景"""
        # 创建空白图像
        img = Image.new('RGB', (self.img_size, self.img_size), color=(135, 206, 235))  # 天空蓝
        
        # 生成道路
        img = self.generate_road(img)
        
        # 生成建筑物
        num_buildings = random.randint(2, 4)
        for _ in range(num_buildings):
            x = random.randint(0, self.img_size-20)
            y = random.randint(5, self.img_size//2-15)
            width = random.randint(15, 25)
            height = random.randint(10, 20)
            color = (random.randint(150, 200), random.randint(150, 200), random.randint(150, 200))
            img = self.generate_building(img, x, y, width, height, color)
        
        # 生成汽车
        num_cars = random.randint(1, 3)
        for _ in range(num_cars):
            x = random.randint(5, self.img_size-20)
            y = random.randint(self.img_size//2+5, self.img_size-15)
            colors = [(255, 0, 0), (0, 0, 255), (0, 255, 0), (255, 255, 0), (255, 0, 255)]
            color = random.choice(colors)
            img = self.generate_car(img, x, y, color)
        
        # 生成交通标志
        if random.random() > 0.5:  # 50%概率生成交通标志
            x = random.randint(10, self.img_size-15)
            y = random.randint(self.img_size//2-10, self.img_size//2+5)
            img = self.generate_traffic_sign(img, x, y)
        
        return img
    
    def __getitem__(self, idx):
        # 设置随机种子以确保可重现性
        random.seed(idx)
        np.random.seed(idx)
        
        # 生成交通场景
        img = self.generate_traffic_scene()
        
        # 转换为tensor
        img_array = np.array(img).astype(np.float32) / 255.0
        img_tensor = torch.from_numpy(img_array).permute(2, 0, 1)
        
        # 归一化到[-1, 1]
        img_tensor = img_tensor * 2.0 - 1.0
        
        return img_tensor, img_tensor  # 输入和目标相同（自监督）

def create_traffic_data_loaders(num_samples=2000, batch_size=32, img_size=64):
    """创建交通场景数据加载器"""
    
    print("正在生成合成交通场景数据集...")
    dataset = SyntheticTrafficDataset(num_samples=num_samples, img_size=img_size)
    
    # 划分训练集和验证集
    train_size = int(0.8 * num_samples)
    val_size = num_samples - train_size
    
    train_dataset = torch.utils.data.Subset(dataset, range(train_size))
    val_dataset = torch.utils.data.Subset(dataset, range(train_size, num_samples))
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    print(f"训练集大小: {len(train_dataset)}")
    print(f"验证集大小: {len(val_dataset)}")
    
    return train_loader, val_loader

def visualize_traffic_samples(data_loader, num_samples=8, save_path="synthetic_traffic_samples.png"):
    """可视化合成交通场景样本"""
    
    # 获取一批数据
    data_iter = iter(data_loader)
    images, _ = next(data_iter)
    
    # 反归一化
    images = (images + 1) / 2  # 从[-1,1]转换到[0,1]
    
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    axes = axes.flatten()
    
    for i in range(min(num_samples, len(images))):
        img = images[i].permute(1, 2, 0).cpu().numpy()
        img = np.clip(img, 0, 1)
        
        axes[i].imshow(img)
        axes[i].set_title(f'Synthetic Traffic Scene {i+1}')
        axes[i].axis('off')
    
    # 隐藏多余的子图
    for i in range(num_samples, len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"合成交通场景样本已保存到: {save_path}")

class TrafficSceneTrainer:
    """交通场景重建训练器"""
    
    def __init__(self, model, train_loader, val_loader, device='cpu'):
        self.model = model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.device = device
        
        # 优化器和损失函数
        self.optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
        self.scheduler = optim.lr_scheduler.StepLR(self.optimizer, step_size=15, gamma=0.7)
        self.criterion = nn.MSELoss()
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.val_psnr = []
    
    def calculate_psnr(self, img1, img2):
        """计算PSNR"""
        mse = torch.mean((img1 - img2) ** 2)
        if mse == 0:
            return float('inf')
        return 20 * torch.log10(1.0 / torch.sqrt(mse))
    
    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        for batch_idx, (data, target) in enumerate(self.train_loader):
            data, target = data.to(self.device), target.to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            output = self.model(data)
            
            # 计算损失
            loss = self.criterion(output, target)
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx % 20 == 0:
                print(f'  Batch {batch_idx}/{len(self.train_loader)}, Loss: {loss.item():.6f}')
        
        return total_loss / num_batches
    
    def validate(self):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        total_psnr = 0
        num_batches = 0
        
        with torch.no_grad():
            for data, target in self.val_loader:
                data, target = data.to(self.device), target.to(self.device)
                
                # 前向传播
                output = self.model(data)
                
                # 计算损失
                loss = self.criterion(output, target)
                total_loss += loss.item()
                
                # 计算PSNR
                psnr = self.calculate_psnr(output, target)
                total_psnr += psnr.item()
                
                num_batches += 1
        
        avg_loss = total_loss / num_batches
        avg_psnr = total_psnr / num_batches
        
        return avg_loss, avg_psnr
    
    def train(self, num_epochs):
        """训练模型"""
        print(f"开始训练交通场景重建模型，共{num_epochs}个epoch...")
        
        best_psnr = 0
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            
            # 训练
            train_loss = self.train_epoch()
            
            # 验证
            val_loss, val_psnr = self.validate()
            
            # 更新学习率
            self.scheduler.step()
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.val_psnr.append(val_psnr)
            
            print(f"训练损失: {train_loss:.6f}")
            print(f"验证损失: {val_loss:.6f}")
            print(f"验证PSNR: {val_psnr:.2f} dB")
            print(f"学习率: {self.scheduler.get_last_lr()[0]:.6f}")
            
            # 保存最佳模型
            if val_psnr > best_psnr:
                best_psnr = val_psnr
                torch.save(self.model.state_dict(), 'best_synthetic_traffic_model.pth')
                print(f"💾 保存最佳模型 (PSNR: {best_psnr:.2f} dB)")
    
    def plot_training_history(self, save_path="synthetic_traffic_training_history.png"):
        """绘制训练历史"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        
        # 损失曲线
        ax1.plot(self.train_losses, label='训练损失', color='blue', linewidth=2)
        ax1.plot(self.val_losses, label='验证损失', color='red', linewidth=2)
        ax1.set_title('训练和验证损失', fontsize=14)
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # PSNR曲线
        ax2.plot(self.val_psnr, label='验证PSNR', color='green', linewidth=2)
        ax2.set_title('验证PSNR', fontsize=14)
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('PSNR (dB)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        print(f"📊 训练历史已保存到: {save_path}")

def test_traffic_reconstruction(model, test_loader, device='cpu', save_path="synthetic_traffic_reconstruction_results.png"):
    """测试交通场景重建"""
    model.eval()
    
    # 获取测试样本
    data_iter = iter(test_loader)
    images, _ = next(data_iter)
    images = images[:6].to(device)  # 取前6个样本
    
    with torch.no_grad():
        reconstructed = model(images)
    
    # 反归一化
    images = (images + 1) / 2
    reconstructed = (reconstructed + 1) / 2
    
    # 计算PSNR
    psnr_values = []
    for i in range(len(images)):
        mse = torch.mean((images[i] - reconstructed[i]) ** 2)
        psnr = 20 * torch.log10(1.0 / torch.sqrt(mse))
        psnr_values.append(psnr.item())
    
    # 可视化
    fig, axes = plt.subplots(2, 6, figsize=(20, 8))
    
    for i in range(6):
        # 原始图像
        img = images[i].permute(1, 2, 0).cpu().numpy()
        img = np.clip(img, 0, 1)
        axes[0, i].imshow(img)
        axes[0, i].set_title(f'Original {i+1}', fontsize=12)
        axes[0, i].axis('off')
        
        # 重建图像
        recon_img = reconstructed[i].permute(1, 2, 0).cpu().numpy()
        recon_img = np.clip(recon_img, 0, 1)
        axes[1, i].imshow(recon_img)
        axes[1, i].set_title(f'Reconstructed {i+1}\nPSNR: {psnr_values[i]:.1f}dB', fontsize=12)
        axes[1, i].axis('off')
    
    plt.suptitle('Synthetic Traffic Scene Reconstruction Results', fontsize=16)
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"🎯 重建结果已保存到: {save_path}")
    print(f"平均PSNR: {np.mean(psnr_values):.2f} dB")

def main():
    """主函数"""
    print("🚗 合成交通场景图像重建Transformer")
    print("=" * 60)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建数据加载器
    print("\n📊 准备合成交通场景数据...")
    train_loader, val_loader = create_traffic_data_loaders(
        num_samples=2000, 
        batch_size=32, 
        img_size=64
    )
    
    # 可视化样本
    print("\n🖼️ 可视化合成交通场景样本...")
    visualize_traffic_samples(train_loader)
    
    # 创建模型
    print("\n🏗️ 创建模型...")
    model = SimpleImageTransformer(
        img_size=64, 
        patch_size=8, 
        embed_dim=256, 
        num_layers=6, 
        num_heads=8
    )
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练器
    trainer = TrafficSceneTrainer(model, train_loader, val_loader, device)
    
    # 训练模型
    print("\n🎯 开始训练...")
    trainer.train(num_epochs=30)
    
    # 绘制训练历史
    trainer.plot_training_history()
    
    # 测试重建效果
    print("\n🧪 测试重建效果...")
    test_traffic_reconstruction(model, val_loader, device)
    
    print("\n✅ 合成交通场景重建完成！")
    print("\n📁 生成的文件:")
    print("- synthetic_traffic_samples.png: 合成交通场景样本")
    print("- synthetic_traffic_training_history.png: 训练历史")
    print("- synthetic_traffic_reconstruction_results.png: 重建结果")
    print("- best_synthetic_traffic_model.pth: 最佳模型权重")

if __name__ == "__main__":
    main()
